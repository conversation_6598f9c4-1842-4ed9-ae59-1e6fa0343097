# Migration Uniwien

## Initiales Setup:

- PHP Version 7.1 installieren
- repo clonen
- config/etc/env.local.php nach app/etc/env.php kopieren
- DB config anpassen
- Werte für web/unsecure/base_url, web/secure/base_url und web/cookie/cookie_path anpassen
- bin/magento app:config:import aufrufen (vor setup:upgrade aufrufen, da ansonsten Fehler geworfen wird: Import failed: Area code is not set)
- bin/magento setup:upgrade aufrufen
- im Verzeichnis vendor/snowdog/frontools npm-shrinkwrap.json anlegen mit folgendem Inhalt: 
`{
  "dependencies": {
    "graceful-fs": {
      "version": "4.2.3"
    }
  }
}`
- npm install --prefix vendor/snowdog/frontools --no-progress --no-spin
- gulp styles --gulpfile vendor/snowdog/frontools/gulpfile.js --area frontend --lang scss --dev

shop sollte jetzt laufen

## Downgrade auf Community-Version
- Änderung composer.json: magento/product-enterprise-edition zu magento/product-community-edition
- composer update
- migrations sql ausführen (dev/tools/downgrade-ee-ce)
- bin/magento setup:upgrade
- evtl. nochmal theme per gulp compilieren

## Upgrade auf Magento 2.3.5
- PHP Version 7.2 installieren
- Anleitung siehe https://devdocs.magento.com/guides/v2.3/comp-mgr/cli/upgrade-with-script.html
- php -f dev/tools/pre_composer_update_2.3.php -- --root='/Users/<USER>/sites/uniwien' --repo=https://repo.magento.com/
- composer update
- rm -rf var/cache/*
- rm -rf var/page_cache/*
- rm -rf generated/code/*
- bin/magento setup:upgrade
- jede menge fehlermeldungen bzgl. SQL-Keys (aktuell per Hand bearbeitet)
- npm install --prefix vendor/snowdog/frontools --no-progress --no-spin
- npm run setup --prefix vendor/snowdog/frontools
- dev/tools/frontools/config/themes.json anpassen und plugins.autoprefixer() in autoprefixer() ändern
- app/design/frontend/UniWien/blank/Magento_Sales/styles/_email.scss, Zeile 67 anpassen, da ein Fehler geworfen wird (TODO)
- gulp styles --gulpfile vendor/snowdog/frontools/gulpfile.esm.js --area frontend --lang scss --dev
- bin/magento cache:flush
- Produkte über Mass-Update 1x neu gespeichert und die Website zugewiesen
- bin/magento index:reindex
- Magento 2 console notice - Fallback to JQueryUI Compat activated => app/design/frontend/UniWien/blank/web/mage/validation.js angepasst

## Fehlermeldungen
Fehlermeldungen beim Upgrade SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP 'SALESRULE_COUPON_RULE_ID_SALESRULE_RULE_ID'; check that column/key exists, query was: ALTER TABLE `salesrule_coupon` DROP FOREIGN KEY `SALESRULE_COUPON_RULE_ID_SALESRULE_RULE_ID`

SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP 'SALESRULE_CUSTOMER_RULE_ID_SALESRULE_RULE_ID'; check that column/key exists, query was: ALTER TABLE `salesrule_customer` DROP FOREIGN KEY `SALESRULE_CUSTOMER_RULE_ID_SALESRULE_RULE_ID`

SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP 'SALESRULE_LABEL_RULE_ID_SALESRULE_RULE_ID'; check that column/key exists, query was: ALTER TABLE `salesrule_label` DROP FOREIGN KEY `SALESRULE_LABEL_RULE_ID_SALESRULE_RULE_ID`

SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'CAT_PRD_ENTT_MDA_GLR_VAL_ENTT_ID_VAL_ID_STORE_ID', query was: ALTER TABLE `catalog_product_entity_media_gallery_value` MODIFY COLUMN `record_id` int(10) UNSIGNED NOT NULL  AUTO_INCREMENT COMMENT "Record ID", ADD INDEX `CAT_PRD_ENTT_MDA_GLR_VAL_ENTT_ID_VAL_ID_STORE_ID` (`entity_id`,`value_id`,`store_id`)
