<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Observer;

use CopeX\MageWorxCategoryOptions\Api\CategoryProductResolverInterface;
use CopeX\MageWorxCategoryOptions\Api\OptionValueGeneratorInterface;
use CopeX\MageWorxCategoryOptions\Model\Metadata\CategorySourceConfig;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;

class ProductSaveObserver implements ObserverInterface
{
    private CategoryProductResolverInterface $categoryProductResolver;
    private OptionValueGeneratorInterface $optionValueGenerator;
    private ResourceConnection $resourceConnection;
    private LoggerInterface $logger;

    public function __construct(
        CategoryProductResolverInterface $categoryProductResolver,
        OptionValueGeneratorInterface $optionValueGenerator,
        ResourceConnection $resourceConnection,
        LoggerInterface $logger
    ) {
        $this->categoryProductResolver = $categoryProductResolver;
        $this->optionValueGenerator = $optionValueGenerator;
        $this->resourceConnection = $resourceConnection;
        $this->logger = $logger;
    }

    public function execute(Observer $observer): void
    {
        try {
            $product = $observer->getEvent()->getProduct();
            $this->updateCategoryBasedOptions($product);
        } catch (\Exception $e) {
            $this->logger->error('Error updating category-based options: ' . $e->getMessage());
        }
    }

    /**
     * Update category-based options when product categories change
     */
    private function updateCategoryBasedOptions($product): void
    {
        $productId = $product->getId();
        $categoryIds = $product->getCategoryIds();

        if (empty($categoryIds)) {
            return;
        }

        // Find all category-based options that might be affected
        $affectedOptions = $this->getAffectedCategoryOptions($categoryIds);

        foreach ($affectedOptions as $optionData) {
            try {
                $this->refreshCategoryOption($optionData);
            } catch (\Exception $e) {
                $this->logger->warning(sprintf(
                    'Failed to refresh category option %d: %s',
                    $optionData['option_id'],
                    $e->getMessage()
                ));
            }
        }
    }

    /**
     * Get category options that might be affected by product category changes
     */
    private function getAffectedCategoryOptions(array $productCategoryIds): array
    {
        $connection = $this->resourceConnection->getConnection();
        $tableName = $this->resourceConnection->getTableName('copex_mageworx_category_options');

        if (!$connection->isTableExists($tableName)) {
            return [];
        }

        $select = $connection->select()
            ->from($tableName)
            ->where('category_ids REGEXP ?', implode('|', $productCategoryIds));

        return $connection->fetchAll($select);
    }

    /**
     * Refresh a single category option
     */
    private function refreshCategoryOption(array $optionData): void
    {
        $optionId = (int) $optionData['option_id'];
        $categoryIds = json_decode($optionData['category_ids'], true) ?: [];
        $filters = json_decode($optionData['filters_json'], true) ?: [];

        if (empty($categoryIds)) {
            return;
        }

        // Resolve products from categories
        $productIds = $this->categoryProductResolver->resolveProductIds($categoryIds, $filters);

        // Update option values
        $config = [
            'category_ids' => $categoryIds,
            'filters' => $filters
        ];

        $this->optionValueGenerator->updateOptionValues($optionId, $productIds, $config);
    }
}
