<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Model;

use CopeX\MageWorxCategoryOptions\Api\OptionValueGeneratorInterface;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Framework\App\ResourceConnection;

class OptionValueGenerator implements OptionValueGeneratorInterface
{
    private CollectionFactory $productCollectionFactory;
    private ResourceConnection $resourceConnection;

    public function __construct(
        CollectionFactory $productCollectionFactory,
        ResourceConnection $resourceConnection
    ) {
        $this->productCollectionFactory = $productCollectionFactory;
        $this->resourceConnection = $resourceConnection;
    }

    public function generateOptionValues(array $productIds, int $optionId, array $config = []): array
    {
        $values = [];

        $collection = $this->productCollectionFactory->create();
        $collection->addIdFilter($productIds);
        $collection->addAttributeToSelect(['name', 'sku']);

        foreach ($collection as $product) {
            $values[] = [
                'title' => $product->getName(),
                'sku' => $product->getSku(),
                'sort_order' => 0,
                'option_id' => $optionId
            ];
        }

        return $values;
    }

    public function updateOptionValues(int $optionId, array $productIds, array $config = []): void
    {
        // Simple implementation: just generate and insert values
        $newValues = $this->generateOptionValues($productIds, $optionId, $config);

        if (!empty($newValues)) {
            $this->insertOptionValues($newValues);
        }
    }

    public function removeCategoryGeneratedValues(int $optionId, string $sourceHash = ''): void
    {
        // Simple implementation - could be enhanced later
    }

    private function insertOptionValues(array $values): void
    {
        $connection = $this->resourceConnection->getConnection();
        $table = $this->resourceConnection->getTableName('catalog_product_option_type_value');

        // Simple insert - just basic data
        $mainData = [];
        foreach ($values as $value) {
            $mainData[] = [
                'option_id' => $value['option_id'],
                'sku' => $value['sku'],
                'sort_order' => $value['sort_order']
            ];
        }

        if (!empty($mainData)) {
            $connection->insertMultiple($table, $mainData);
            $this->insertOptionValueTitles($values);
        }
    }

    private function insertOptionValueTitles(array $values): void
    {
        $connection = $this->resourceConnection->getConnection();
        $titleTable = $this->resourceConnection->getTableName('catalog_product_option_type_title');

        // Get the inserted option_type_ids
        $select = $connection->select()
            ->from($this->resourceConnection->getTableName('catalog_product_option_type_value'), ['option_type_id', 'sku'])
            ->where('option_id = ?', $values[0]['option_id'])
            ->where('sku IN (?)', array_column($values, 'sku'));

        $insertedValues = $connection->fetchPairs($select);

        $titleData = [];
        foreach ($values as $value) {
            if (isset($insertedValues[$value['sku']])) {
                $titleData[] = [
                    'option_type_id' => $insertedValues[$value['sku']],
                    'store_id' => 0,
                    'title' => $value['title']
                ];
            }
        }

        if (!empty($titleData)) {
            $connection->insertMultiple($titleTable, $titleData);
        }
    }
}
