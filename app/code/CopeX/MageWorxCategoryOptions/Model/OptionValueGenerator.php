<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Model;

use CopeX\MageWorxCategoryOptions\Api\OptionValueGeneratorInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;

class OptionValueGenerator implements OptionValueGeneratorInterface
{
    private ProductRepositoryInterface $productRepository;
    private CollectionFactory $productCollectionFactory;
    private ResourceConnection $resourceConnection;
    private LoggerInterface $logger;

    public function __construct(
        ProductRepositoryInterface $productRepository,
        CollectionFactory $productCollectionFactory,
        ResourceConnection $resourceConnection,
        LoggerInterface $logger
    ) {
        $this->productRepository = $productRepository;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->resourceConnection = $resourceConnection;
        $this->logger = $logger;
    }

    public function generateOptionValues(array $productIds, int $optionId, array $config = []): array
    {
        $values = [];
        $sourceHash = $this->generateSourceHash($config);

        $collection = $this->productCollectionFactory->create();
        $collection->addIdFilter($productIds);
        $collection->addAttributeToSelect(['name', 'price', 'sku']);

        foreach ($collection as $product) {
            $values[] = [
                'title' => $product->getName(),
                'price' => $product->getPrice() ?: 0,
                'price_type' => 'fixed',
                'sku' => $product->getSku(),
                'sort_order' => 0,
                'option_id' => $optionId,
                'copex_source' => 'category:' . $sourceHash,
                'copex_product_id' => $product->getId()
            ];
        }

        return $values;
    }

    public function updateOptionValues(int $optionId, array $productIds, array $config = []): void
    {
        $connection = $this->resourceConnection->getConnection();
        $sourceHash = $this->generateSourceHash($config);

        try {
            $connection->beginTransaction();

            // Remove existing category-generated values for this source
            $this->removeCategoryGeneratedValues($optionId, $sourceHash);

            // Generate new values
            $newValues = $this->generateOptionValues($productIds, $optionId, $config);

            // Insert new values
            if (!empty($newValues)) {
                $this->insertOptionValues($newValues);
            }

            $connection->commit();
        } catch (\Exception $e) {
            $connection->rollBack();
            $this->logger->error('Failed to update option values: ' . $e->getMessage());
            throw $e;
        }
    }

    public function removeCategoryGeneratedValues(int $optionId, string $sourceHash = ''): void
    {
        $connection = $this->resourceConnection->getConnection();
        $table = $this->resourceConnection->getTableName('catalog_product_option_type_value');

        $where = ['option_id = ?' => $optionId];

        if ($sourceHash) {
            $where['copex_source = ?'] = 'category:' . $sourceHash;
        } else {
            $where[] = 'copex_source LIKE "category:%"';
        }

        // Also remove from title table
        $titleTable = $this->resourceConnection->getTableName('catalog_product_option_type_title');
        $select = $connection->select()
            ->from($table, ['option_type_id'])
            ->where(implode(' AND ', array_keys($where)), array_values($where));

        $optionTypeIds = $connection->fetchCol($select);

        if (!empty($optionTypeIds)) {
            $connection->delete($titleTable, ['option_type_id IN (?)' => $optionTypeIds]);
        }

        $connection->delete($table, $where);
    }

    private function generateSourceHash(array $config): string
    {
        return md5(json_encode($config));
    }

    private function insertOptionValues(array $values): void
    {
        $connection = $this->resourceConnection->getConnection();
        $table = $this->resourceConnection->getTableName('catalog_product_option_type_value');

        // First insert into main table
        $mainData = [];
        foreach ($values as $value) {
            $mainData[] = [
                'option_id' => $value['option_id'],
                'sku' => $value['sku'],
                'sort_order' => $value['sort_order'],
                'copex_source' => $value['copex_source'] ?? null,
                'copex_product_id' => $value['copex_product_id'] ?? null
            ];
        }

        if (!empty($mainData)) {
            $connection->insertMultiple($table, $mainData);

            // Get inserted IDs and insert titles
            $this->insertOptionValueTitles($values, $connection);
        }
    }

    private function insertOptionValueTitles(array $values, $connection): void
    {
        $titleTable = $this->resourceConnection->getTableName('catalog_product_option_type_title');

        // Get the inserted option_type_ids
        $select = $connection->select()
            ->from($this->resourceConnection->getTableName('catalog_product_option_type_value'), ['option_type_id', 'sku'])
            ->where('option_id = ?', $values[0]['option_id'])
            ->where('sku IN (?)', array_column($values, 'sku'));

        $insertedValues = $connection->fetchPairs($select);

        $titleData = [];
        foreach ($values as $value) {
            if (isset($insertedValues[$value['sku']])) {
                $titleData[] = [
                    'option_type_id' => $insertedValues[$value['sku']],
                    'store_id' => 0,
                    'title' => $value['title']
                ];
            }
        }

        if (!empty($titleData)) {
            $connection->insertMultiple($titleTable, $titleData);
        }
    }
}
