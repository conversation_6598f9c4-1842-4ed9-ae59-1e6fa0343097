<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class Config
{
    private const XML_PATH_DEFAULT_STATUS = 'copex_mageworx/category_options/default_status';
    private const XML_PATH_DEFAULT_VISIBILITY = 'copex_mageworx/category_options/default_visibility';
    private const XML_PATH_DEFAULT_SALABLE = 'copex_mageworx/category_options/default_salable';
    private const XML_PATH_DEFAULT_SORT_BY = 'copex_mageworx/category_options/default_sort_by';
    private const XML_PATH_DEFAULT_SORT_DIRECTION = 'copex_mageworx/category_options/default_sort_direction';
    private const XML_PATH_DEFAULT_LIMIT = 'copex_mageworx/category_options/default_limit';
    private const XML_PATH_MAX_LIMIT = 'copex_mageworx/category_options/max_limit';

    private ScopeConfigInterface $scopeConfig;

    public function __construct(ScopeConfigInterface $scopeConfig)
    {
        $this->scopeConfig = $scopeConfig;
    }

    public function getDefaultStatus(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_DEFAULT_STATUS,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getDefaultVisibility(?int $storeId = null): array
    {
        $value = $this->scopeConfig->getValue(
            self::XML_PATH_DEFAULT_VISIBILITY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
        
        return $value ? explode(',', $value) : [];
    }

    public function getDefaultSalable(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_DEFAULT_SALABLE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getDefaultSortBy(?int $storeId = null): string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_DEFAULT_SORT_BY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        ) ?: 'name';
    }

    public function getDefaultSortDirection(?int $storeId = null): string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_DEFAULT_SORT_DIRECTION,
            ScopeInterface::SCOPE_STORE,
            $storeId
        ) ?: 'asc';
    }

    public function getDefaultLimit(?int $storeId = null): int
    {
        return (int) $this->scopeConfig->getValue(
            self::XML_PATH_DEFAULT_LIMIT,
            ScopeInterface::SCOPE_STORE,
            $storeId
        ) ?: 100;
    }

    public function getMaxLimit(?int $storeId = null): int
    {
        return (int) $this->scopeConfig->getValue(
            self::XML_PATH_MAX_LIMIT,
            ScopeInterface::SCOPE_STORE,
            $storeId
        ) ?: 1000;
    }

    public function getDefaultFilters(?int $storeId = null): array
    {
        return [
            'status' => $this->getDefaultStatus($storeId),
            'visibility' => $this->getDefaultVisibility($storeId),
            'salable' => $this->getDefaultSalable($storeId),
            'sort_by' => $this->getDefaultSortBy($storeId),
            'sort_direction' => $this->getDefaultSortDirection($storeId),
            'limit' => $this->getDefaultLimit($storeId)
        ];
    }
}
