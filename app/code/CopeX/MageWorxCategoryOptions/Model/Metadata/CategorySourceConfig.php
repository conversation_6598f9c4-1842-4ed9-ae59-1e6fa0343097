<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Model\Metadata;

class CategorySourceConfig
{
    private array $categoryIds;
    private array $filters;
    private string $sourceHash;

    public function __construct(array $categoryIds = [], array $filters = [])
    {
        $this->categoryIds = $categoryIds;
        $this->filters = $filters;
        $this->sourceHash = $this->generateHash();
    }

    public function getCategoryIds(): array
    {
        return $this->categoryIds;
    }

    public function setCategoryIds(array $categoryIds): self
    {
        $this->categoryIds = $categoryIds;
        $this->sourceHash = $this->generateHash();
        return $this;
    }

    public function getFilters(): array
    {
        return $this->filters;
    }

    public function setFilters(array $filters): self
    {
        $this->filters = $filters;
        $this->sourceHash = $this->generateHash();
        return $this;
    }

    public function getSourceHash(): string
    {
        return $this->sourceHash;
    }

    public function toArray(): array
    {
        return [
            'category_ids' => $this->categoryIds,
            'filters' => $this->filters,
            'source_hash' => $this->sourceHash
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['category_ids'] ?? [],
            $data['filters'] ?? []
        );
    }

    public function toJson(): string
    {
        return json_encode($this->toArray());
    }

    public static function fromJson(string $json): self
    {
        $data = json_decode($json, true) ?: [];
        return self::fromArray($data);
    }

    private function generateHash(): string
    {
        return md5(json_encode([
            'category_ids' => $this->categoryIds,
            'filters' => $this->filters
        ]));
    }
}
