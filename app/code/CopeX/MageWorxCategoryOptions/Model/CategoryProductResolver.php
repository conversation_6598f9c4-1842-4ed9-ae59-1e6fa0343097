<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Model;

use CopeX\MageWorxCategoryOptions\Api\CategoryProductResolverInterface;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Catalog\Model\Product\Attribute\Source\Status;

class CategoryProductResolver implements CategoryProductResolverInterface
{
    private CollectionFactory $productCollectionFactory;

    public function __construct(CollectionFactory $productCollectionFactory)
    {
        $this->productCollectionFactory = $productCollectionFactory;
    }

    public function resolveProductIds(array $categoryIds, array $filters = []): array
    {
        $collection = $this->getProductsCollection($categoryIds, $filters);
        return $collection->getAllIds();
    }

    public function getProductsCollection(array $categoryIds, array $filters = []): \Magento\Catalog\Model\ResourceModel\Product\Collection
    {
        $collection = $this->productCollectionFactory->create();

        // Add category filter
        if (!empty($categoryIds)) {
            $collection->addCategoriesFilter(['in' => $categoryIds]);
        }

        // Apply basic filters
        $collection->addAttributeToFilter('status', Status::STATUS_ENABLED);

        // Apply limit (default 100)
        $limit = $filters['limit'] ?? 100;
        if ($limit > 0) {
            $collection->setPageSize($limit);
        }

        // Add basic attributes
        $collection->addAttributeToSelect(['name', 'sku', 'price']);

        return $collection;
    }
}
