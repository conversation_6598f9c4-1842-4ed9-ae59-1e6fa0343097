<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Model;

use CopeX\MageWorxCategoryOptions\Api\CategoryProductResolverInterface;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Catalog\Model\Product\Visibility;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Framework\Module\Manager as ModuleManager;
use Magento\InventorySalesApi\Api\IsProductSalableInterface;
use Magento\InventorySalesApi\Api\GetStockIdForCurrentWebsiteInterface;
use Magento\CatalogInventory\Api\StockRegistryInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class CategoryProductResolver implements CategoryProductResolverInterface
{
    private CollectionFactory $productCollectionFactory;
    private ModuleManager $moduleManager;
    private ?IsProductSalableInterface $isProductSalable;
    private ?GetStockIdForCurrentWebsiteInterface $getStockIdForCurrentWebsite;
    private StockRegistryInterface $stockRegistry;
    private StoreManagerInterface $storeManager;
    private LoggerInterface $logger;

    public function __construct(
        CollectionFactory $productCollectionFactory,
        ModuleManager $moduleManager,
        StoreManagerInterface $storeManager,
        StockRegistryInterface $stockRegistry,
        LoggerInterface $logger,
        ?IsProductSalableInterface $isProductSalable = null,
        ?GetStockIdForCurrentWebsiteInterface $getStockIdForCurrentWebsite = null
    ) {
        $this->productCollectionFactory = $productCollectionFactory;
        $this->moduleManager = $moduleManager;
        $this->storeManager = $storeManager;
        $this->stockRegistry = $stockRegistry;
        $this->logger = $logger;
        $this->isProductSalable = $isProductSalable;
        $this->getStockIdForCurrentWebsite = $getStockIdForCurrentWebsite;
    }

    public function resolveProductIds(array $categoryIds, array $filters = []): array
    {
        $collection = $this->getProductsCollection($categoryIds, $filters);
        return $collection->getAllIds();
    }

    public function getProductsCollection(array $categoryIds, array $filters = []): \Magento\Catalog\Model\ResourceModel\Product\Collection
    {
        $collection = $this->productCollectionFactory->create();
        
        // Add category filter
        if (!empty($categoryIds)) {
            $collection->addCategoriesFilter(['in' => $categoryIds]);
        }

        // Apply status filter
        if (isset($filters['status']) && $filters['status']) {
            $collection->addAttributeToFilter('status', Status::STATUS_ENABLED);
        }

        // Apply visibility filter
        if (isset($filters['visibility']) && !empty($filters['visibility'])) {
            $collection->addAttributeToFilter('visibility', ['in' => $filters['visibility']]);
        }

        // Apply salable filter
        if (isset($filters['salable']) && $filters['salable']) {
            $this->applySalableFilter($collection);
        }

        // Apply sorting
        if (isset($filters['sort_by'])) {
            $this->applySorting($collection, $filters['sort_by'], $filters['sort_direction'] ?? 'asc');
        }

        // Apply limit
        if (isset($filters['limit']) && $filters['limit'] > 0) {
            $collection->setPageSize($filters['limit']);
        }

        return $collection;
    }

    private function applySalableFilter(\Magento\Catalog\Model\ResourceModel\Product\Collection $collection): void
    {
        if ($this->moduleManager->isEnabled('Magento_InventorySalesApi') && 
            $this->isProductSalable && 
            $this->getStockIdForCurrentWebsite) {
            // MSI is enabled
            try {
                $websiteId = $this->storeManager->getWebsite()->getId();
                $stockId = $this->getStockIdForCurrentWebsite->execute($websiteId);
                
                $collection->getSelect()->joinInner(
                    ['stock_status' => $collection->getTable('inventory_stock_' . $stockId)],
                    'e.sku = stock_status.sku AND stock_status.is_salable = 1',
                    []
                );
            } catch (\Exception $e) {
                $this->logger->warning('MSI salable filter failed, falling back to legacy: ' . $e->getMessage());
                $this->applyLegacySalableFilter($collection);
            }
        } else {
            // Legacy stock management
            $this->applyLegacySalableFilter($collection);
        }
    }

    private function applyLegacySalableFilter(\Magento\Catalog\Model\ResourceModel\Product\Collection $collection): void
    {
        $collection->joinField(
            'stock_status',
            'cataloginventory_stock_status',
            'stock_status',
            'product_id=entity_id',
            ['stock_status' => 1],
            'inner'
        );
    }

    private function applySorting(\Magento\Catalog\Model\ResourceModel\Product\Collection $collection, string $sortBy, string $direction): void
    {
        switch ($sortBy) {
            case 'name':
                $collection->addAttributeToSort('name', $direction);
                break;
            case 'price':
                $collection->addAttributeToSort('price', $direction);
                break;
            case 'position':
                $collection->addAttributeToSort('position', $direction);
                break;
            default:
                $collection->addAttributeToSort('entity_id', $direction);
        }
    }
}
