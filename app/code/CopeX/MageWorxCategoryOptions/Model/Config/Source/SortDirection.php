<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;

class SortDirection implements OptionSourceInterface
{
    public function toOptionArray(): array
    {
        return [
            ['value' => 'asc', 'label' => __('Ascending')],
            ['value' => 'desc', 'label' => __('Descending')]
        ];
    }
}
