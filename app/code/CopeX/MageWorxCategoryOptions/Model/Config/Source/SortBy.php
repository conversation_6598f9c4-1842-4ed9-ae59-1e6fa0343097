<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;

class SortBy implements OptionSourceInterface
{
    public function toOptionArray(): array
    {
        return [
            ['value' => 'name', 'label' => __('Name')],
            ['value' => 'price', 'label' => __('Price')],
            ['value' => 'position', 'label' => __('Position')],
            ['value' => 'entity_id', 'label' => __('Product ID')]
        ];
    }
}
