<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;

class SourceMode implements OptionSourceInterface
{
    public function toOptionArray(): array
    {
        return [
            ['value' => '', 'label' => __('-- Please Select --')],
            ['value' => 'sku', 'label' => __('Product by SKU')],
            ['value' => 'id', 'label' => __('Product by ID')],
            ['value' => 'category', 'label' => __('Products by Category')]
        ];
    }
}
