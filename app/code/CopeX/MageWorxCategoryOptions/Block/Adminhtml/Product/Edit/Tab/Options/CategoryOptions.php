<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Block\Adminhtml\Product\Edit\Tab\Options;

use Magento\Backend\Block\Template;
use Magento\Backend\Block\Template\Context;
use CopeX\MageWorxCategoryOptions\Model\Config;
use Magento\Catalog\Model\CategoryFactory;
use Magento\Framework\Registry;

class CategoryOptions extends Template
{
    private Config $config;
    private CategoryFactory $categoryFactory;
    private Registry $registry;

    public function __construct(
        Context $context,
        Config $config,
        CategoryFactory $categoryFactory,
        Registry $registry,
        array $data = []
    ) {
        $this->config = $config;
        $this->categoryFactory = $categoryFactory;
        $this->registry = $registry;
        parent::__construct($context, $data);
    }

    /**
     * Get current product
     */
    public function getProduct()
    {
        return $this->registry->registry('current_product');
    }

    /**
     * Get default configuration
     */
    public function getDefaultConfig(): array
    {
        return $this->config->getDefaultFilters();
    }

    /**
     * Get category tree as JSON
     */
    public function getCategoryTreeJson(): string
    {
        $categories = $this->getCategoryTree();
        return json_encode($categories);
    }

    /**
     * Get category tree
     */
    private function getCategoryTree(): array
    {
        $rootCategory = $this->categoryFactory->create()->load(2); // Default root category
        return $this->buildCategoryTree($rootCategory);
    }

    /**
     * Build category tree recursively
     */
    private function buildCategoryTree($category): array
    {
        $result = [
            'id' => $category->getId(),
            'name' => $category->getName(),
            'level' => $category->getLevel(),
            'children' => []
        ];

        $children = $category->getChildrenCategories();
        foreach ($children as $child) {
            if ($child->getIsActive()) {
                $result['children'][] = $this->buildCategoryTree($child);
            }
        }

        return $result;
    }
}
