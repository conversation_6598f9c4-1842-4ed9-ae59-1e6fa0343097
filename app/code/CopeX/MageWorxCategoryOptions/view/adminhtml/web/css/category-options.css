/* CopeX MageWorx Category Options Styles */

.category-options-container {
    margin: 10px 0;
}

.category-tree {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    background: #fff;
}

.category-tree ul {
    list-style: none;
    padding-left: 20px;
    margin: 0;
}

.category-tree li {
    margin: 5px 0;
}

.category-tree label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 2px 0;
}

.category-tree label:hover {
    background-color: #f5f5f5;
}

.category-tree input[type="checkbox"] {
    margin-right: 8px;
}

.category-tree span {
    font-size: 13px;
    color: #333;
}

.field-category-options {
    margin-bottom: 20px;
}

.field-category-options._disabled {
    opacity: 0.5;
    pointer-events: none;
}

.field-category-options._error .admin__field-control {
    border-color: #e22626;
}

.copex-category-fields {
    background: #f8f8f8;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 3px;
    margin-top: 10px;
}

.copex-filter-section {
    background: #fff;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
    margin-top: 10px;
}

.copex-filter-section .admin__field-set-title {
    margin-bottom: 10px;
    font-weight: 600;
}

.copex-inline-fields {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.copex-inline-fields .admin__field {
    flex: 1;
    min-width: 200px;
}

@media (max-width: 768px) {
    .copex-inline-fields {
        flex-direction: column;
    }
    
    .category-tree {
        max-height: 200px;
    }
}
