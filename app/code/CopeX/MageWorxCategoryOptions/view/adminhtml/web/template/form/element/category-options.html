<!-- ko if: visible -->
<div class="admin__field field-category-options" data-bind="
    css: {
        '_error': error,
        '_required': required,
        '_disabled': disabled
    }">
    
    <!-- ko if: label -->
    <label class="admin__field-label" data-bind="
        attr: {
            for: uid
        }">
        <span data-bind="text: label"></span>
        <!-- ko if: required -->
        <span class="admin__field-required">*</span>
        <!-- /ko -->
    </label>
    <!-- /ko -->
    
    <div class="admin__field-control">
        <div class="category-options-container">
            <!-- Category selection and filter fields will be rendered here -->
            <div data-bind="template: getTemplate()"></div>
        </div>
        
        <!-- ko if: notice -->
        <div class="admin__field-note" data-bind="html: notice"></div>
        <!-- /ko -->
        
        <!-- ko if: error -->
        <div class="admin__field-error" data-bind="text: error"></div>
        <!-- /ko -->
    </div>
</div>
<!-- /ko -->
