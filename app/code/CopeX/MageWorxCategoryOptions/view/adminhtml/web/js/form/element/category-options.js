define([
    'Magento_Ui/js/form/element/abstract',
    'uiRegistry'
], function (Abstract, registry) {
    'use strict';

    return Abstract.extend({
        defaults: {
            template: 'CopeX_MageWorxCategoryOptions/form/element/category-options'
        },

        /**
         * Initialize component
         */
        initialize: function () {
            this._super();
            this.initSourceModeObserver();
            return this;
        },

        /**
         * Initialize source mode observer
         */
        initSourceModeObserver: function () {
            var self = this;
            
            registry.async(this.parentName + '.source_mode')(function (sourceModeField) {
                sourceModeField.on('value', function (value) {
                    self.toggleCategoryFields(value === 'category');
                });
                
                // Initial state
                self.toggleCategoryFields(sourceModeField.value() === 'category');
            });
        },

        /**
         * Toggle category fields visibility
         */
        toggleCategoryFields: function (show) {
            var fields = [
                'category_ids',
                'filters.filter_status',
                'filters.filter_visibility', 
                'filters.filter_salable',
                'filters.sort_by',
                'filters.sort_direction',
                'filters.limit'
            ];

            fields.forEach(function (fieldName) {
                registry.async(this.parentName + '.' + fieldName)(function (field) {
                    field.visible(show);
                }.bind(this));
            }.bind(this));
        }
    });
});
