define([
    'jquery',
    'mage/template',
    'jquery/ui'
], function ($, mageTemplate) {
    'use strict';

    $.widget('copex.categoryOptions', {
        options: {
            categoryTree: [],
            defaultConfig: {},
            sourceModeSelector: '#copex-source-mode',
            categoryFieldsSelector: '#copex-category-fields',
            categoryTreeSelector: '#copex-category-tree'
        },

        _create: function () {
            this._bind();
            this._initCategoryTree();
            this._setDefaults();
        },

        _bind: function () {
            var self = this;
            
            $(this.options.sourceModeSelector).on('change', function () {
                self._toggleCategoryFields($(this).val() === 'category');
            });
        },

        _toggleCategoryFields: function (show) {
            if (show) {
                $(this.options.categoryFieldsSelector).show();
            } else {
                $(this.options.categoryFieldsSelector).hide();
            }
        },

        _initCategoryTree: function () {
            var self = this;
            var $tree = $(this.options.categoryTreeSelector);
            
            if (this.options.categoryTree && this.options.categoryTree.length > 0) {
                var treeHtml = this._buildTreeHtml(this.options.categoryTree);
                $tree.html(treeHtml);
                
                // Initialize tree functionality
                $tree.find('input[type="checkbox"]').on('change', function () {
                    self._handleCategorySelection($(this));
                });
            }
        },

        _buildTreeHtml: function (categories) {
            var html = '<ul class="category-tree">';
            
            $.each(categories, function (index, category) {
                html += '<li>';
                html += '<label>';
                html += '<input type="checkbox" value="' + category.id + '" data-level="' + category.level + '">';
                html += '<span>' + category.name + '</span>';
                html += '</label>';
                
                if (category.children && category.children.length > 0) {
                    html += this._buildTreeHtml(category.children);
                }
                
                html += '</li>';
            }.bind(this));
            
            html += '</ul>';
            return html;
        },

        _handleCategorySelection: function ($checkbox) {
            var isChecked = $checkbox.is(':checked');
            var $parent = $checkbox.closest('li');
            
            // Handle child categories
            $parent.find('ul input[type="checkbox"]').prop('checked', isChecked);
            
            // Handle parent categories
            if (isChecked) {
                $parent.parents('li').children('label').children('input[type="checkbox"]').prop('checked', true);
            }
        },

        _setDefaults: function () {
            var config = this.options.defaultConfig;
            
            if (config.status) {
                $('#copex-filter-status').prop('checked', true);
            }
            
            if (config.salable) {
                $('#copex-filter-salable').prop('checked', true);
            }
            
            if (config.sort_by) {
                $('#copex-sort-by').val(config.sort_by);
            }
            
            if (config.limit) {
                $('#copex-limit').val(config.limit);
            }
        },

        getSelectedCategories: function () {
            var categories = [];
            $(this.options.categoryTreeSelector + ' input[type="checkbox"]:checked').each(function () {
                categories.push(parseInt($(this).val()));
            });
            return categories;
        },

        getFilterConfig: function () {
            return {
                status: $('#copex-filter-status').is(':checked'),
                salable: $('#copex-filter-salable').is(':checked'),
                sort_by: $('#copex-sort-by').val(),
                limit: parseInt($('#copex-limit').val()) || 100
            };
        }
    });

    return $.copex.categoryOptions;
});
