<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    
    <!-- Category Options Fieldset -->
    <fieldset name="category_options" sortOrder="100">
        <settings>
            <label translate="true">Category-based Options</label>
            <collapsible>true</collapsible>
            <opened>false</opened>
        </settings>
        
        <!-- Source Mode Selection -->
        <field name="source_mode" formElement="select" sortOrder="10">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Source Mode</label>
                <dataScope>source_mode</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">false</rule>
                </validation>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="CopeX\MageWorxCategoryOptions\Model\Config\Source\SourceMode"/>
                    </settings>
                </select>
            </formElements>
        </field>
        
        <!-- Category Selection -->
        <field name="category_ids" formElement="multiselect" sortOrder="20">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Categories</label>
                <dataScope>category_ids</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">false</rule>
                </validation>
            </settings>
            <formElements>
                <multiselect>
                    <settings>
                        <options class="Magento\Catalog\Ui\Component\Product\Form\Categories\Options"/>
                    </settings>
                </multiselect>
            </formElements>
        </field>
        
        <!-- Filter Options -->
        <fieldset name="filters" sortOrder="30">
            <settings>
                <label translate="true">Filter Options</label>
                <collapsible>true</collapsible>
                <opened>true</opened>
            </settings>
            
            <field name="filter_status" formElement="checkbox" sortOrder="10">
                <settings>
                    <dataType>boolean</dataType>
                    <label translate="true">Only Enabled Products</label>
                    <dataScope>filter_status</dataScope>
                </settings>
                <formElements>
                    <checkbox>
                        <settings>
                            <valueMap>
                                <map name="false" xsi:type="string">0</map>
                                <map name="true" xsi:type="string">1</map>
                            </valueMap>
                            <prefer>toggle</prefer>
                        </settings>
                    </checkbox>
                </formElements>
            </field>
            
            <field name="filter_visibility" formElement="multiselect" sortOrder="20">
                <settings>
                    <dataType>text</dataType>
                    <label translate="true">Product Visibility</label>
                    <dataScope>filter_visibility</dataScope>
                </settings>
                <formElements>
                    <multiselect>
                        <settings>
                            <options class="Magento\Catalog\Model\Product\Visibility"/>
                        </settings>
                    </multiselect>
                </formElements>
            </field>
            
            <field name="filter_salable" formElement="checkbox" sortOrder="30">
                <settings>
                    <dataType>boolean</dataType>
                    <label translate="true">Only Salable Products</label>
                    <dataScope>filter_salable</dataScope>
                </settings>
                <formElements>
                    <checkbox>
                        <settings>
                            <valueMap>
                                <map name="false" xsi:type="string">0</map>
                                <map name="true" xsi:type="string">1</map>
                            </valueMap>
                            <prefer>toggle</prefer>
                        </settings>
                    </checkbox>
                </formElements>
            </field>
            
            <field name="sort_by" formElement="select" sortOrder="40">
                <settings>
                    <dataType>text</dataType>
                    <label translate="true">Sort By</label>
                    <dataScope>sort_by</dataScope>
                </settings>
                <formElements>
                    <select>
                        <settings>
                            <options class="CopeX\MageWorxCategoryOptions\Model\Config\Source\SortBy"/>
                        </settings>
                    </select>
                </formElements>
            </field>
            
            <field name="sort_direction" formElement="select" sortOrder="50">
                <settings>
                    <dataType>text</dataType>
                    <label translate="true">Sort Direction</label>
                    <dataScope>sort_direction</dataScope>
                </settings>
                <formElements>
                    <select>
                        <settings>
                            <options class="CopeX\MageWorxCategoryOptions\Model\Config\Source\SortDirection"/>
                        </settings>
                    </select>
                </formElements>
            </field>
            
            <field name="limit" formElement="input" sortOrder="60">
                <settings>
                    <dataType>number</dataType>
                    <label translate="true">Limit</label>
                    <dataScope>limit</dataScope>
                    <validation>
                        <rule name="validate-digits" xsi:type="boolean">true</rule>
                        <rule name="validate-greater-than-zero" xsi:type="boolean">true</rule>
                    </validation>
                </settings>
            </field>
            
        </fieldset>
        
    </fieldset>
    
</form>
