<?php
/**
 * @var \CopeX\MageWorxCategoryOptions\Block\Adminhtml\Product\Edit\Tab\Options\CategoryOptions $block
 */
?>

<script type="text/x-magento-init">
{
    "*": {
        "CopeX_MageWorxCategoryOptions/js/category-options": {
            "categoryTree": <?= $block->getCategoryTreeJson() ?>,
            "defaultConfig": <?= json_encode($block->getDefaultConfig()) ?>
        }
    }
}
</script>

<div id="copex-category-options-container" style="display: none;">
    <div class="admin__field-set">
        <div class="admin__field-set-title">
            <strong><?= __('Category-based Options') ?></strong>
        </div>
        
        <div class="admin__field">
            <label class="admin__field-label">
                <span><?= __('Source Mode') ?></span>
            </label>
            <div class="admin__field-control">
                <select id="copex-source-mode" class="admin__control-select">
                    <option value=""><?= __('-- Please Select --') ?></option>
                    <option value="sku"><?= __('Product by SKU') ?></option>
                    <option value="id"><?= __('Product by ID') ?></option>
                    <option value="category"><?= __('Products by Category') ?></option>
                </select>
            </div>
        </div>
        
        <div id="copex-category-fields" style="display: none;">
            <div class="admin__field">
                <label class="admin__field-label">
                    <span><?= __('Categories') ?></span>
                </label>
                <div class="admin__field-control">
                    <div id="copex-category-tree"></div>
                </div>
            </div>
            
            <div class="admin__field-set">
                <div class="admin__field-set-title">
                    <strong><?= __('Filter Options') ?></strong>
                </div>
                
                <div class="admin__field">
                    <div class="admin__field-control">
                        <div class="admin__field-option">
                            <input type="checkbox" id="copex-filter-status" class="admin__control-checkbox">
                            <label for="copex-filter-status" class="admin__field-label">
                                <span><?= __('Only Enabled Products') ?></span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="admin__field">
                    <div class="admin__field-control">
                        <div class="admin__field-option">
                            <input type="checkbox" id="copex-filter-salable" class="admin__control-checkbox">
                            <label for="copex-filter-salable" class="admin__field-label">
                                <span><?= __('Only Salable Products') ?></span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="admin__field">
                    <label class="admin__field-label">
                        <span><?= __('Sort By') ?></span>
                    </label>
                    <div class="admin__field-control">
                        <select id="copex-sort-by" class="admin__control-select">
                            <option value="name"><?= __('Name') ?></option>
                            <option value="price"><?= __('Price') ?></option>
                            <option value="position"><?= __('Position') ?></option>
                        </select>
                    </div>
                </div>
                
                <div class="admin__field">
                    <label class="admin__field-label">
                        <span><?= __('Limit') ?></span>
                    </label>
                    <div class="admin__field-control">
                        <input type="number" id="copex-limit" class="admin__control-text" min="1" max="1000" value="100">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
