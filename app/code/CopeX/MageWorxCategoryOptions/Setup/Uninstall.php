<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Setup;

use Magento\Framework\Setup\UninstallInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Framework\Setup\ModuleContextInterface;

class Uninstall implements UninstallInterface
{
    public function uninstall(SchemaSetupInterface $setup, ModuleContextInterface $context): void
    {
        $installer = $setup;
        $installer->startSetup();

        // Drop the main configuration table
        $tableName = $installer->getTable('copex_mageworx_category_options');
        if ($installer->getConnection()->isTableExists($tableName)) {
            $installer->getConnection()->dropTable($tableName);
        }

        // Remove custom columns from catalog_product_option_type_value table
        $optionValueTable = $installer->getTable('catalog_product_option_type_value');
        
        if ($installer->getConnection()->tableColumnExists($optionValueTable, 'copex_source')) {
            $installer->getConnection()->dropColumn($optionValueTable, 'copex_source');
        }

        if ($installer->getConnection()->tableColumnExists($optionValueTable, 'copex_product_id')) {
            $installer->getConnection()->dropColumn($optionValueTable, 'copex_product_id');
        }

        $installer->endSetup();
    }
}
