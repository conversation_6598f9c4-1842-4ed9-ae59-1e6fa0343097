<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Setup;

use Magento\Framework\Setup\InstallSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Framework\DB\Ddl\Table;

class InstallSchema implements InstallSchemaInterface
{
    public function install(SchemaSetupInterface $setup, ModuleContextInterface $context): void
    {
        $installer = $setup;
        $installer->startSetup();

        // Create copex_mageworx_category_options table
        $table = $installer->getConnection()->newTable(
            $installer->getTable('copex_mageworx_category_options')
        )->addColumn(
            'id',
            Table::TYPE_INTEGER,
            null,
            ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
            'ID'
        )->addColumn(
            'option_id',
            Table::TYPE_INTEGER,
            null,
            ['unsigned' => true, 'nullable' => false],
            'Option ID'
        )->addColumn(
            'category_ids',
            Table::TYPE_TEXT,
            null,
            ['nullable' => false],
            'Category IDs JSON'
        )->addColumn(
            'filters_json',
            Table::TYPE_TEXT,
            null,
            ['nullable' => true],
            'Filters JSON'
        )->addColumn(
            'source_hash',
            Table::TYPE_TEXT,
            32,
            ['nullable' => false],
            'Source Hash'
        )->addColumn(
            'created_at',
            Table::TYPE_TIMESTAMP,
            null,
            ['nullable' => false, 'default' => Table::TIMESTAMP_INIT],
            'Created At'
        )->addColumn(
            'updated_at',
            Table::TYPE_TIMESTAMP,
            null,
            ['nullable' => false, 'default' => Table::TIMESTAMP_INIT_UPDATE],
            'Updated At'
        )->addIndex(
            $installer->getIdxName('copex_mageworx_category_options', ['option_id']),
            ['option_id']
        )->addIndex(
            $installer->getIdxName('copex_mageworx_category_options', ['source_hash']),
            ['source_hash']
        )->addForeignKey(
            $installer->getFkName('copex_mageworx_category_options', 'option_id', 'catalog_product_option', 'option_id'),
            'option_id',
            $installer->getTable('catalog_product_option'),
            'option_id',
            Table::ACTION_CASCADE
        )->setComment(
            'CopeX MageWorx Category Options Configuration'
        );

        $installer->getConnection()->createTable($table);

        // Add copex_source and copex_product_id columns to catalog_product_option_type_value table
        $optionValueTable = $installer->getTable('catalog_product_option_type_value');
        
        if (!$installer->getConnection()->tableColumnExists($optionValueTable, 'copex_source')) {
            $installer->getConnection()->addColumn(
                $optionValueTable,
                'copex_source',
                [
                    'type' => Table::TYPE_TEXT,
                    'length' => 255,
                    'nullable' => true,
                    'comment' => 'CopeX Source Identifier'
                ]
            );
        }

        if (!$installer->getConnection()->tableColumnExists($optionValueTable, 'copex_product_id')) {
            $installer->getConnection()->addColumn(
                $optionValueTable,
                'copex_product_id',
                [
                    'type' => Table::TYPE_INTEGER,
                    'unsigned' => true,
                    'nullable' => true,
                    'comment' => 'CopeX Product ID Reference'
                ]
            );
        }

        $installer->endSetup();
    }
}
