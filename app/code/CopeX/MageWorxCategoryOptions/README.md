# CopeX MageWorx Category Options

## Beschreibung

Dieses Magento 2 Modul erweitert die Funktionalität von MageWorx Custom Options um die Möglichkeit, Custom Option Values automatisch basierend auf Produktkategorien zu generieren. Zusätzlich zur bestehenden "Product by ID/SKU" Funktionalität wird eine neue Option "Products by Category" hinzugefügt.

## Features

- **Kategoriebasierte Option Values**: Automatische Generierung von Option Values aus Produkten bestimmter Kategorien
- **Erweiterte Filter**: Status, Sichtbarkeit, Lagerbestand (Salable), Sortierung und Limits
- **Idempotente Updates**: Bereits generierte Values werden intelligent aktualisiert ohne manuelle Values zu beeinträchtigen
- **CLI-Synchronisation**: Befehl zur manuellen Synchronisation aller kategoriebasierten Optionen
- **MSI-Unterstützung**: Vollständige Unterstützung für Magento Multi-Source Inventory
- **Performance-optimiert**: Paginierung und Limits zur Vermeidung von Memory-Problemen
- **Mehrsprachig**: Deutsche und englische Übersetzungen

## Systemanforderungen

- Magento 2.4.6 - 2.4.8
- PHP 8.1 oder 8.2
- MageWorx OptionBase Modul

## Installation

### 1. Modul-Dateien kopieren

Kopieren Sie alle Dateien in das Verzeichnis:
```
app/code/CopeX/MageWorxCategoryOptions/
```

### 2. Modul aktivieren

```bash
bin/magento module:enable CopeX_MageWorxCategoryOptions
```

### 3. Setup ausführen

```bash
bin/magento setup:upgrade
bin/magento setup:di:compile
bin/magento setup:static-content:deploy
```

### 4. Cache leeren

```bash
bin/magento cache:flush
```

## Konfiguration

### Admin-Konfiguration

Navigieren Sie zu **Stores → Configuration → Catalog → CopeX MageWorx Extensions → Category Options**

Verfügbare Einstellungen:
- **Default Product Status Filter**: Standardmäßig nur aktivierte Produkte verwenden
- **Default Visibility Filter**: Standard-Sichtbarkeitseinstellungen
- **Default Salable Filter**: Standardmäßig nur verkaufbare Produkte verwenden
- **Default Sort By**: Standard-Sortierung (Name, Preis, Position)
- **Default Sort Direction**: Sortierrichtung (Aufsteigend/Absteigend)
- **Default Limit**: Standard-Anzahl von Produkten pro Option
- **Maximum Limit**: Maximales Limit zur Performance-Optimierung

## Verwendung

### 1. Custom Options erstellen/bearbeiten

1. Gehen Sie zu **Catalog → Products**
2. Bearbeiten Sie ein Produkt
3. Wechseln Sie zum Tab **Customizable Options**
4. Erstellen Sie eine neue Option oder bearbeiten Sie eine bestehende
5. Wählen Sie als **Source Mode** die Option **"Products by Category"**

### 2. Kategorien und Filter konfigurieren

Nach Auswahl des Category-Modus werden zusätzliche Felder angezeigt:

- **Categories**: Multiselect zur Auswahl der gewünschten Kategorien
- **Filter Options**:
  - Only Enabled Products: Nur aktivierte Produkte einbeziehen
  - Product Visibility: Sichtbarkeitsfilter
  - Only Salable Products: Nur verkaufbare Produkte
  - Sort By: Sortierung der Produkte
  - Limit: Maximale Anzahl von Option Values

### 3. Speichern und Generierung

Beim Speichern des Produkts werden automatisch die Option Values basierend auf den konfigurierten Kategorien und Filtern generiert.

## CLI-Befehle

### Synchronisation aller kategoriebasierten Optionen

```bash
bin/magento copex:custom-options:sync-by-category --all
```

### Synchronisation für ein bestimmtes Produkt

```bash
bin/magento copex:custom-options:sync-by-category --product-id=123
```

## Technische Details

### Service Contracts

- `CategoryProductResolverInterface`: Auflösung von Produkten aus Kategorien
- `OptionValueGeneratorInterface`: Generierung und Verwaltung von Option Values

### Datenbank-Tabellen

Das Modul erstellt eine zusätzliche Tabelle `copex_mageworx_category_options` zur Speicherung der Kategorie-Konfigurationen und erweitert die Standard-Tabelle `catalog_product_option_type_value` um zwei Spalten:
- `copex_source`: Kennzeichnung der Quelle (z.B. "category:hash")
- `copex_product_id`: Referenz auf die ursprüngliche Produkt-ID

### Plugin-System

Das Modul verwendet Plugins anstatt Preferences um maximale Kompatibilität zu gewährleisten:
- `OptionSaveByCategoryPlugin`: Verarbeitung beim Speichern von Optionen

### Observer

- `ProductSaveObserver`: Automatische Aktualisierung bei Änderungen an Produktkategorien

## Berechtigungen

Das Modul definiert eine neue ACL-Ressource:
- `CopeX_MageWorxCategoryOptions::manage`: Verwaltung kategoriebasierter Custom Options
- `CopeX_MageWorxCategoryOptions::config`: Konfiguration des Moduls

## Performance-Hinweise

- Das Modul verwendet Paginierung und Limits zur Vermeidung von Memory-Problemen
- Die Standard-Limits können in der Konfiguration angepasst werden
- Bei sehr großen Kategorien sollten die Limits entsprechend gesetzt werden

## Kompatibilität

- Vollständig kompatibel mit MageWorx Custom Options
- Unterstützt sowohl MSI als auch Legacy Stock Management
- Keine Änderungen an Vendor-Code erforderlich
- Kompatibel mit Magento 2.4.6 - 2.4.8

## Support

Bei Fragen oder Problemen wenden Sie sich an das CopeX-Entwicklungsteam.

## Lizenz

Dieses Modul ist proprietär und für den internen Gebrauch bestimmt.
