# CopeX MageWorx Category Options (Simplified)

## Beschreibung

Dieses **vereinfachte** Magento 2 Modul erweitert die Funktionalität von MageWorx Custom Options um die Möglichkeit, Custom Option Values automatisch basierend auf Produktkategorien zu generieren.

## Features

- **Kategoriebasierte Option Values**: Einfache Generierung von Option Values aus Produkten bestimmter Kategorien
- **Grundlegende Konfiguration**: Aktivierung/Deaktivierung und Produktlimit-Einstellungen
- **CLI-Befehl**: Einfacher Sync-Befehl (Demo-Implementierung)
- **Service Contracts**: Saubere API-Struktur für Erweiterungen
- **Plugin-System**: Keine Änderungen am Vendor-Code erforderlich

## Systemanforderungen

- Magento 2.4.6 - 2.4.8
- PHP 8.1 oder 8.2
- MageWorx OptionBase Modul

## Installation

### 1. Modul-<PERSON>ien kopieren

Kopieren Sie alle Dateien in das Verzeichnis:
```
app/code/CopeX/MageWorxCategoryOptions/
```

### 2. Modul aktivieren

```bash
bin/magento module:enable CopeX_MageWorxCategoryOptions
```

### 3. Setup ausführen

```bash
bin/magento setup:upgrade
bin/magento setup:di:compile
bin/magento setup:static-content:deploy
```

### 4. Cache leeren

```bash
bin/magento cache:flush
```

## Konfiguration

### Admin-Konfiguration

Navigieren Sie zu **Stores → Configuration → Catalog → CopeX MageWorx Extensions**

Verfügbare Einstellungen:
- **Enable Category Options**: Aktivierung der Funktionalität
- **Default Product Limit**: Standard-Anzahl von Produkten pro Option (Standard: 100)

## Verwendung

### Vereinfachte Implementierung

Diese Version ist eine **vereinfachte Demo-Implementierung**. Die Kernfunktionalität ist vorhanden, aber die UI-Integration ist minimal.

Das Plugin `OptionSaveByCategoryPlugin` demonstriert, wie kategoriebasierte Option Values generiert werden können:

```php
// Beispiel: Kategorie-IDs über Request-Parameter
$categoryIds = $this->request->getParam('copex_category_ids');

if (!empty($categoryIds) && is_array($categoryIds)) {
    // Produkte aus Kategorien auflösen
    $productIds = $this->categoryProductResolver->resolveProductIds($categoryIds);

    // Option Values generieren
    $this->optionValueGenerator->updateOptionValues($optionId, $productIds);
}
```

## CLI-Befehle

### Demo-Sync-Befehl

```bash
bin/magento copex:custom-options:sync-by-category
```

Dieser Befehl ist eine vereinfachte Demo-Implementierung.

## Technische Details

### Service Contracts

- `CategoryProductResolverInterface`: Einfache Auflösung von Produkten aus Kategorien
- `OptionValueGeneratorInterface`: Grundlegende Generierung von Option Values

### Plugin-System

Das Modul verwendet ein Plugin für maximale Kompatibilität:
- `OptionSaveByCategoryPlugin`: Demo-Verarbeitung beim Speichern von Optionen

### Vereinfachungen

Diese Version verzichtet auf:
- Komplexe UI-Komponenten
- Datenbank-Schema-Änderungen
- Erweiterte Filter und Sortierungen
- MSI-Integration
- Observer-Pattern
- Umfangreiche Konfigurationsoptionen

## Berechtigungen

Das Modul definiert eine ACL-Ressource:
- `CopeX_MageWorxCategoryOptions::config`: Konfiguration des Moduls

## Kompatibilität

- Grundlegende Kompatibilität mit MageWorx Custom Options
- Keine Änderungen an Vendor-Code erforderlich
- Kompatibel mit Magento 2.4.6 - 2.4.8

## Erweiterungsmöglichkeiten

Diese vereinfachte Version kann erweitert werden um:
- Komplexe UI-Komponenten für die Admin-Oberfläche
- Erweiterte Filter und Sortieroptionen
- Datenbank-Schema für persistente Konfiguration
- MSI-Integration
- Observer für automatische Updates
- Umfangreiche Konfigurationsoptionen

## Lizenz

Dieses Modul ist proprietär und für den internen Gebrauch bestimmt.
