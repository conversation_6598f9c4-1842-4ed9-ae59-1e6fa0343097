# CopeX MageWorx Category Options (Simplified)

## Beschreibung

Dieses **vereinfachte** Magento 2 Modul erweitert die Funktionalität von MageWorx Custom Options um die Möglichkeit, Custom Option Values automatisch basierend auf Produktkategorien zu generieren.

## Features

- **Kategoriebasierte Option Values**: Einfache Generierung von Option Values aus Produkten bestimmter Kategorien
- **Grundlegende Konfiguration**: Aktivierung/Deaktivierung und Produktlimit-Einstellungen
- **CLI-Befehl**: Einfacher Sync-Befehl (Demo-Implementierung)
- **Service Contracts**: Saubere API-Struktur für Erweiterungen
- **Plugin-System**: Keine Änderungen am Vendor-Code erforderlich

## Systemanforderungen

- Magento 2.4.6 - 2.4.8
- PHP 8.1 oder 8.2
- MageWorx OptionBase Modul

## Installation

### 1. Modul-<PERSON>ien kopieren

Kopieren Sie alle Dateien in das Verzeichnis:
```
app/code/CopeX/MageWorxCategoryOptions/
```

### 2. Modul aktivieren

```bash
bin/magento module:enable CopeX_MageWorxCategoryOptions
```

### 3. Setup ausführen

```bash
bin/magento setup:upgrade
bin/magento setup:di:compile
bin/magento setup:static-content:deploy
```

### 4. Cache leeren

```bash
bin/magento cache:flush
```

## Konfiguration

### Admin-Konfiguration

Navigieren Sie zu **Stores → Configuration → Catalog → CopeX MageWorx Extensions**

Verfügbare Einstellungen:
- **Enable Category Options**: Aktivierung der Funktionalität
- **Default Product Limit**: Standard-Anzahl von Produkten pro Option (Standard: 100)

## Verwendung

### Methode 1: Über Admin-Interface (Einfach)

1. **Gehen Sie zu Catalog → Products**
2. **Bearbeiten Sie ein Produkt**
3. **Wechseln Sie zum Tab "Customizable Options"**
4. **Erstellen Sie eine neue Option** mit einem speziellen Titel-Format:
   ```
   CATEGORY:1,2,3
   ```
   Wobei `1,2,3` die Kategorie-IDs sind, aus denen Produkte geladen werden sollen.

5. **Speichern Sie das Produkt**
   - Das Plugin erkennt automatisch das "CATEGORY:" Muster
   - Generiert Option Values aus allen Produkten der angegebenen Kategorien

**Beispiele für Option-Titel:**
- `CATEGORY:4` - Lädt Produkte aus Kategorie 4
- `CATEGORY:2,5,8` - Lädt Produkte aus Kategorien 2, 5 und 8
- `CATEGORY:10` - Lädt Produkte aus Kategorie 10

### Methode 2: Über CLI-Befehl

```bash
# Alle Optionen mit CATEGORY: Muster synchronisieren
bin/magento copex:custom-options:sync-by-category

# Spezifische Option mit bestimmten Kategorien aktualisieren
bin/magento copex:custom-options:sync-by-category --option-id=123 --category-ids=1,2,3
```

### Schritt-für-Schritt Anleitung

1. **Kategorie-IDs finden:**
   - Gehen Sie zu Catalog → Categories
   - Bearbeiten Sie eine Kategorie
   - Die ID steht in der URL: `/admin/catalog/category/edit/id/4/`

2. **Custom Option erstellen:**
   - Produkt bearbeiten → Customizable Options
   - Add Option → Dropdown/Multiple Select
   - **Option Title:** `CATEGORY:4` (für Kategorie 4)
   - **Required:** Nach Bedarf
   - Speichern

3. **Automatische Generierung:**
   - Beim Speichern werden automatisch alle Produkte aus Kategorie 4 als Option Values hinzugefügt
   - Jedes Produkt wird mit Name und SKU als Option Value erstellt

## Technische Details

### Service Contracts

- `CategoryProductResolverInterface`: Einfache Auflösung von Produkten aus Kategorien
- `OptionValueGeneratorInterface`: Grundlegende Generierung von Option Values

### Plugin-System

Das Modul verwendet ein Plugin für maximale Kompatibilität:
- `OptionSaveByCategoryPlugin`: Demo-Verarbeitung beim Speichern von Optionen

### Vereinfachungen

Diese Version verzichtet auf:
- Komplexe UI-Komponenten
- Datenbank-Schema-Änderungen
- Erweiterte Filter und Sortierungen
- MSI-Integration
- Observer-Pattern
- Umfangreiche Konfigurationsoptionen

## Berechtigungen

Das Modul definiert eine ACL-Ressource:
- `CopeX_MageWorxCategoryOptions::config`: Konfiguration des Moduls

## Kompatibilität

- Grundlegende Kompatibilität mit MageWorx Custom Options
- Keine Änderungen an Vendor-Code erforderlich
- Kompatibel mit Magento 2.4.6 - 2.4.8

## Erweiterungsmöglichkeiten

Diese vereinfachte Version kann erweitert werden um:
- Komplexe UI-Komponenten für die Admin-Oberfläche
- Erweiterte Filter und Sortieroptionen
- Datenbank-Schema für persistente Konfiguration
- MSI-Integration
- Observer für automatische Updates
- Umfangreiche Konfigurationsoptionen

## Lizenz

Dieses Modul ist proprietär und für den internen Gebrauch bestimmt.
