<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <!-- Service Contract Implementations -->
    <preference for="CopeX\MageWorxCategoryOptions\Api\CategoryProductResolverInterface"
                type="CopeX\MageWorxCategoryOptions\Model\CategoryProductResolver"/>
    <preference for="CopeX\MageWorxCategoryOptions\Api\OptionValueGeneratorInterface"
                type="CopeX\MageWorxCategoryOptions\Model\OptionValueGenerator"/>

    <!-- Console Commands -->
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="copex_sync_category_options" xsi:type="object">CopeX\MageWorxCategoryOptions\Console\Command\SyncByCategoryCommand</item>
            </argument>
        </arguments>
    </type>

    <!-- Configuration -->
    <type name="CopeX\MageWorxCategoryOptions\Model\Config">
        <arguments>
            <argument name="scopeConfig" xsi:type="object">Magento\Framework\App\Config\ScopeConfigInterface</argument>
        </arguments>
    </type>

    <!-- Category Product Resolver with MSI support -->
    <type name="CopeX\MageWorxCategoryOptions\Model\CategoryProductResolver">
        <arguments>
            <argument name="isProductSalable" xsi:type="object">Magento\InventorySalesApi\Api\IsProductSalableInterface\Proxy</argument>
            <argument name="getStockIdForCurrentWebsite" xsi:type="object">Magento\InventorySalesApi\Api\GetStockIdForCurrentWebsiteInterface\Proxy</argument>
        </arguments>
    </type>

    <!-- Plugins -->
    <type name="Magento\Catalog\Model\Product\Option">
        <plugin name="copex_category_options_save" type="CopeX\MageWorxCategoryOptions\Plugin\OptionSaveByCategoryPlugin" sortOrder="10"/>
    </type>

</config>
