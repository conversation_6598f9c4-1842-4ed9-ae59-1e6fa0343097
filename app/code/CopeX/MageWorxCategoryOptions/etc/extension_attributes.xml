<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Api/etc/extension_attributes.xsd">
    
    <extension_attributes for="Magento\Catalog\Api\Data\ProductCustomOptionInterface">
        <attribute code="copex_category_config" type="string"/>
        <attribute code="copex_source_mode" type="string"/>
    </extension_attributes>
    
    <extension_attributes for="Magento\Catalog\Api\Data\ProductCustomOptionValueInterface">
        <attribute code="copex_source" type="string"/>
        <attribute code="copex_product_id" type="int"/>
    </extension_attributes>
    
</config>
