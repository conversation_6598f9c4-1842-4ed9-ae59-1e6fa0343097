<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="copex_mageworx" translate="label" type="text" sortOrder="300" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>CopeX MageWorx Extensions</label>
            <tab>catalog</tab>
            <resource>CopeX_MageWorxCategoryOptions::config</resource>

            <group id="category_options" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Category Options</label>

                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Category Options</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable category-based custom options functionality</comment>
                </field>

                <field id="default_limit" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Default Product Limit</label>
                    <validate>validate-digits validate-greater-than-zero</validate>
                    <comment>Default number of products to include per option (default: 100)</comment>
                </field>

            </group>
        </section>
    </system>
</config>
