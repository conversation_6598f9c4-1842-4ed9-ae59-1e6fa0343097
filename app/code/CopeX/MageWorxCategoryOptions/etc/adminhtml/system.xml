<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="copex_mageworx" translate="label" type="text" sortOrder="300" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>CopeX MageWorx Extensions</label>
            <tab>catalog</tab>
            <resource>CopeX_MageWorxCategoryOptions::config</resource>
            
            <group id="category_options" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Category Options</label>
                
                <field id="default_status" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Default Product Status Filter</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable product status filter by default (only enabled products)</comment>
                </field>
                
                <field id="default_visibility" translate="label comment" type="multiselect" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Default Visibility Filter</label>
                    <source_model>Magento\Catalog\Model\Product\Visibility</source_model>
                    <comment>Default visibility settings for category-based options</comment>
                </field>
                
                <field id="default_salable" translate="label comment" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Default Salable Filter</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable salable filter by default (only products in stock)</comment>
                </field>
                
                <field id="default_sort_by" translate="label comment" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Default Sort By</label>
                    <source_model>CopeX\MageWorxCategoryOptions\Model\Config\Source\SortBy</source_model>
                    <comment>Default sorting for products in category-based options</comment>
                </field>
                
                <field id="default_sort_direction" translate="label comment" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Default Sort Direction</label>
                    <source_model>CopeX\MageWorxCategoryOptions\Model\Config\Source\SortDirection</source_model>
                    <comment>Default sort direction</comment>
                </field>
                
                <field id="default_limit" translate="label comment" type="text" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Default Limit</label>
                    <validate>validate-digits validate-greater-than-zero</validate>
                    <comment>Default maximum number of products to include in options</comment>
                </field>
                
                <field id="max_limit" translate="label comment" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Maximum Limit</label>
                    <validate>validate-digits validate-greater-than-zero</validate>
                    <comment>Maximum allowed limit to prevent performance issues</comment>
                </field>
                
            </group>
        </section>
    </system>
</config>
