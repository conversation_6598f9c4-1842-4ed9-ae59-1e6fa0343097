<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magento_Backend::stores">
                    <resource id="Magento_Backend::stores_settings">
                        <resource id="Magento_Config::config">
                            <resource id="CopeX_MageWorxCategoryOptions::config" title="CopeX MageWorx Category Options Configuration" />
                        </resource>
                    </resource>
                </resource>
                <resource id="Magento_Catalog::catalog">
                    <resource id="Magento_Catalog::products">
                        <resource id="CopeX_MageWorxCategoryOptions::manage" title="Manage Category-based Custom Options" />
                    </resource>
                </resource>
            </resource>
        </resources>
    </acl>
</config>
