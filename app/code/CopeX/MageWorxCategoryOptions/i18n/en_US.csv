"CopeX MageWorx Extensions","CopeX MageWorx Extensions"
"Category Options","Category Options"
"Default Product Status Filter","Default Product Status Filter"
"Enable product status filter by default (only enabled products)","Enable product status filter by default (only enabled products)"
"Default Visibility Filter","Default Visibility Filter"
"Default visibility settings for category-based options","Default visibility settings for category-based options"
"Default Salable Filter","Default Salable Filter"
"Enable salable filter by default (only products in stock)","Enable salable filter by default (only products in stock)"
"Default Sort By","Default Sort By"
"Default sorting for products in category-based options","Default sorting for products in category-based options"
"Default Sort Direction","Default Sort Direction"
"Default sort direction","Default sort direction"
"Default Limit","Default Limit"
"Default maximum number of products to include in options","Default maximum number of products to include in options"
"Maximum Limit","Maximum Limit"
"Maximum allowed limit to prevent performance issues","Maximum allowed limit to prevent performance issues"
"Manage Category-based Custom Options","Manage Category-based Custom Options"
"CopeX MageWorx Category Options Configuration","CopeX MageWorx Category Options Configuration"
"-- Please Select --","-- Please Select --"
"Product by SKU","Product by SKU"
"Product by ID","Product by ID"
"Products by Category","Products by Category"
"Name","Name"
"Price","Price"
"Position","Position"
"Product ID","Product ID"
"Ascending","Ascending"
"Descending","Descending"
"Category-based Options","Category-based Options"
"Source Mode","Source Mode"
"Categories","Categories"
"Filter Options","Filter Options"
"Only Enabled Products","Only Enabled Products"
"Product Visibility","Product Visibility"
"Only Salable Products","Only Salable Products"
"Sort By","Sort By"
"Sort Direction","Sort Direction"
"Limit","Limit"
