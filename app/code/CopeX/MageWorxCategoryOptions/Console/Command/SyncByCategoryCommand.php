<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Console\Command;

use Magento\Framework\Console\Cli;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class SyncByCategoryCommand extends Command
{
    public function __construct(string $name = null)
    {
        parent::__construct($name);
    }

    protected function configure(): void
    {
        $this->setName('copex:custom-options:sync-by-category')
            ->setDescription('Simple sync command for category-based custom options');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Category-based custom options sync command</info>');
        $output->writeln('<comment>This is a simplified implementation</comment>');

        return Cli::RETURN_SUCCESS;
    }

    /**
     * Get category options to sync
     */
    private function getCategoryOptions(?string $productId): array
    {
        $connection = $this->resourceConnection->getConnection();
        $categoryOptionsTable = $this->resourceConnection->getTableName('copex_mageworx_category_options');
        $productOptionsTable = $this->resourceConnection->getTableName('catalog_product_option');

        if (!$connection->isTableExists($categoryOptionsTable)) {
            return [];
        }

        $select = $connection->select()
            ->from(['co' => $categoryOptionsTable])
            ->join(
                ['po' => $productOptionsTable],
                'co.option_id = po.option_id',
                ['product_id']
            );

        if ($productId) {
            $select->where('po.product_id = ?', $productId);
        }

        return $connection->fetchAll($select);
    }

    /**
     * Sync individual category option
     */
    private function syncCategoryOption(array $optionData): void
    {
        $optionId = (int) $optionData['option_id'];
        $categoryIds = json_decode($optionData['category_ids'], true) ?: [];
        $filters = json_decode($optionData['filters_json'], true) ?: [];

        if (empty($categoryIds)) {
            throw new \Exception('No category IDs found for option');
        }

        // Resolve products from categories
        $productIds = $this->categoryProductResolver->resolveProductIds($categoryIds, $filters);

        if (empty($productIds)) {
            throw new \Exception('No products found for specified categories');
        }

        // Update option values
        $config = [
            'category_ids' => $categoryIds,
            'filters' => $filters
        ];

        $this->optionValueGenerator->updateOptionValues($optionId, $productIds, $config);

        // Update timestamp
        $this->updateSyncTimestamp($optionId);
    }

    /**
     * Update sync timestamp
     */
    private function updateSyncTimestamp(int $optionId): void
    {
        $connection = $this->resourceConnection->getConnection();
        $tableName = $this->resourceConnection->getTableName('copex_mageworx_category_options');

        $connection->update(
            $tableName,
            ['updated_at' => date('Y-m-d H:i:s')],
            ['option_id = ?' => $optionId]
        );
    }
}
