<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Console\Command;

use CopeX\MageWorxCategoryOptions\Api\CategoryProductResolverInterface;
use CopeX\MageWorxCategoryOptions\Api\OptionValueGeneratorInterface;
use Magento\Catalog\Model\ResourceModel\Product\Option\CollectionFactory;
use Magento\Framework\Console\Cli;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class SyncByCategoryCommand extends Command
{
    private CategoryProductResolverInterface $categoryProductResolver;
    private OptionValueGeneratorInterface $optionValueGenerator;
    private CollectionFactory $optionCollectionFactory;

    public function __construct(
        CategoryProductResolverInterface $categoryProductResolver,
        OptionValueGeneratorInterface $optionValueGenerator,
        CollectionFactory $optionCollectionFactory,
        string $name = null
    ) {
        $this->categoryProductResolver = $categoryProductResolver;
        $this->optionValueGenerator = $optionValueGenerator;
        $this->optionCollectionFactory = $optionCollectionFactory;
        parent::__construct($name);
    }

    protected function configure(): void
    {
        $this->setName('copex:custom-options:sync-by-category')
            ->setDescription('Sync category-based custom options (looks for CATEGORY: pattern in option titles)')
            ->addOption('category-ids', 'c', InputOption::VALUE_REQUIRED, 'Category IDs (comma-separated)')
            ->addOption('option-id', 'o', InputOption::VALUE_REQUIRED, 'Specific option ID to update');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Starting category-based custom options sync...</info>');

        $categoryIds = $input->getOption('category-ids');
        $optionId = $input->getOption('option-id');

        if ($categoryIds && $optionId) {
            // Direct sync with provided parameters
            $categoryIdArray = array_map('intval', explode(',', $categoryIds));
            $this->syncOption((int)$optionId, $categoryIdArray, $output);
        } else {
            // Find options with CATEGORY: pattern
            $this->syncCategoryOptions($output);
        }

        $output->writeln('<info>Sync completed!</info>');
        return Cli::RETURN_SUCCESS;
    }

    private function syncCategoryOptions(OutputInterface $output): void
    {
        $collection = $this->optionCollectionFactory->create();
        $collection->addFieldToFilter('title', ['like' => 'CATEGORY:%']);

        $count = 0;
        foreach ($collection as $option) {
            $title = $option->getTitle();
            if (strpos($title, 'CATEGORY:') === 0) {
                $categoryPart = substr($title, 9);
                $categoryIds = array_map('intval', explode(',', $categoryPart));

                $this->syncOption((int)$option->getId(), $categoryIds, $output);
                $count++;
            }
        }

        $output->writeln("<info>Processed {$count} category-based options</info>");
    }

    private function syncOption(int $optionId, array $categoryIds, OutputInterface $output): void
    {
        $output->writeln("Processing option ID {$optionId} with categories: " . implode(',', $categoryIds));

        $productIds = $this->categoryProductResolver->resolveProductIds($categoryIds);

        if (!empty($productIds)) {
            $this->optionValueGenerator->updateOptionValues($optionId, $productIds);
            $output->writeln("  -> Generated " . count($productIds) . " option values");
        } else {
            $output->writeln("  -> No products found in specified categories");
        }
    }


}
