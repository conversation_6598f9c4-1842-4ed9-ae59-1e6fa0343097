<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Console\Command;

use Magento\Framework\Console\Cli;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class SyncByCategoryCommand extends Command
{
    public function __construct(string $name = null)
    {
        parent::__construct($name);
    }

    protected function configure(): void
    {
        $this->setName('copex:custom-options:sync-by-category')
            ->setDescription('Simple sync command for category-based custom options');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Category-based custom options sync command</info>');
        $output->writeln('<comment>This is a simplified implementation</comment>');

        return Cli::RETURN_SUCCESS;
    }


}
