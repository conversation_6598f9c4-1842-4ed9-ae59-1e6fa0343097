<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Console\Command;

use CopeX\MageWorxCategoryOptions\Api\CategoryProductResolverInterface;
use CopeX\MageWorxCategoryOptions\Api\OptionValueGeneratorInterface;
use CopeX\MageWorxCategoryOptions\Model\Metadata\CategorySourceConfig;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Console\Cli;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Helper\ProgressBar;

class SyncByCategoryCommand extends Command
{
    private const OPTION_PRODUCT_ID = 'product-id';
    private const OPTION_ALL = 'all';

    private CategoryProductResolverInterface $categoryProductResolver;
    private OptionValueGeneratorInterface $optionValueGenerator;
    private ResourceConnection $resourceConnection;

    public function __construct(
        CategoryProductResolverInterface $categoryProductResolver,
        OptionValueGeneratorInterface $optionValueGenerator,
        ResourceConnection $resourceConnection,
        string $name = null
    ) {
        $this->categoryProductResolver = $categoryProductResolver;
        $this->optionValueGenerator = $optionValueGenerator;
        $this->resourceConnection = $resourceConnection;
        parent::__construct($name);
    }

    protected function configure(): void
    {
        $this->setName('copex:custom-options:sync-by-category')
            ->setDescription('Synchronize category-based custom options')
            ->addOption(
                self::OPTION_PRODUCT_ID,
                'p',
                InputOption::VALUE_OPTIONAL,
                'Specific product ID to sync'
            )
            ->addOption(
                self::OPTION_ALL,
                'a',
                InputOption::VALUE_NONE,
                'Sync all category-based options'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Starting category-based custom options synchronization...</info>');

        try {
            $productId = $input->getOption(self::OPTION_PRODUCT_ID);
            $syncAll = $input->getOption(self::OPTION_ALL);

            if (!$productId && !$syncAll) {
                $output->writeln('<error>Please specify either --product-id or --all option</error>');
                return Cli::RETURN_FAILURE;
            }

            $categoryOptions = $this->getCategoryOptions($productId);

            if (empty($categoryOptions)) {
                $output->writeln('<info>No category-based options found to sync</info>');
                return Cli::RETURN_SUCCESS;
            }

            $output->writeln(sprintf('<info>Found %d category-based options to sync</info>', count($categoryOptions)));

            $progressBar = new ProgressBar($output, count($categoryOptions));
            $progressBar->start();

            $syncedCount = 0;
            $errorCount = 0;

            foreach ($categoryOptions as $optionData) {
                try {
                    $this->syncCategoryOption($optionData);
                    $syncedCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    $output->writeln(sprintf(
                        '<error>Error syncing option ID %d: %s</error>',
                        $optionData['option_id'],
                        $e->getMessage()
                    ));
                }
                $progressBar->advance();
            }

            $progressBar->finish();
            $output->writeln('');

            $output->writeln(sprintf(
                '<info>Synchronization completed. Synced: %d, Errors: %d</info>',
                $syncedCount,
                $errorCount
            ));

            return $errorCount > 0 ? Cli::RETURN_FAILURE : Cli::RETURN_SUCCESS;

        } catch (\Exception $e) {
            $output->writeln('<error>Error during synchronization: ' . $e->getMessage() . '</error>');
            return Cli::RETURN_FAILURE;
        }
    }

    /**
     * Get category options to sync
     */
    private function getCategoryOptions(?string $productId): array
    {
        $connection = $this->resourceConnection->getConnection();
        $categoryOptionsTable = $this->resourceConnection->getTableName('copex_mageworx_category_options');
        $productOptionsTable = $this->resourceConnection->getTableName('catalog_product_option');

        if (!$connection->isTableExists($categoryOptionsTable)) {
            return [];
        }

        $select = $connection->select()
            ->from(['co' => $categoryOptionsTable])
            ->join(
                ['po' => $productOptionsTable],
                'co.option_id = po.option_id',
                ['product_id']
            );

        if ($productId) {
            $select->where('po.product_id = ?', $productId);
        }

        return $connection->fetchAll($select);
    }

    /**
     * Sync individual category option
     */
    private function syncCategoryOption(array $optionData): void
    {
        $optionId = (int) $optionData['option_id'];
        $categoryIds = json_decode($optionData['category_ids'], true) ?: [];
        $filters = json_decode($optionData['filters_json'], true) ?: [];

        if (empty($categoryIds)) {
            throw new \Exception('No category IDs found for option');
        }

        // Resolve products from categories
        $productIds = $this->categoryProductResolver->resolveProductIds($categoryIds, $filters);

        if (empty($productIds)) {
            throw new \Exception('No products found for specified categories');
        }

        // Update option values
        $config = [
            'category_ids' => $categoryIds,
            'filters' => $filters
        ];

        $this->optionValueGenerator->updateOptionValues($optionId, $productIds, $config);

        // Update timestamp
        $this->updateSyncTimestamp($optionId);
    }

    /**
     * Update sync timestamp
     */
    private function updateSyncTimestamp(int $optionId): void
    {
        $connection = $this->resourceConnection->getConnection();
        $tableName = $this->resourceConnection->getTableName('copex_mageworx_category_options');

        $connection->update(
            $tableName,
            ['updated_at' => date('Y-m-d H:i:s')],
            ['option_id = ?' => $optionId]
        );
    }
}
