{"name": "copex/mageworx-category-options", "description": "Extends MageWorx Custom Options with category-based product selection", "type": "magento2-module", "version": "1.0.0", "license": "proprietary", "authors": [{"name": "CopeX Development Team", "email": "<EMAIL>"}], "require": {"php": "^8.1|^8.2", "magento/framework": "^103.0", "magento/module-catalog": "^104.0", "magento/module-backend": "^102.0", "magento/module-ui": "^101.2", "mageworx/module-option-base": "*"}, "suggest": {"magento/module-inventory-sales-api": "For MSI support"}, "autoload": {"files": ["registration.php"], "psr-4": {"CopeX\\MageWorxCategoryOptions\\": ""}}, "extra": {"map": [["*", "CopeX/MageWorxCategoryOptions"]]}}