<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Plugin;

use CopeX\MageWorxCategoryOptions\Api\CategoryProductResolverInterface;
use CopeX\MageWorxCategoryOptions\Api\OptionValueGeneratorInterface;
use Magento\Catalog\Model\Product\Option;
use Magento\Framework\App\RequestInterface;

class OptionSaveByCategoryPlugin
{
    private CategoryProductResolverInterface $categoryProductResolver;
    private OptionValueGeneratorInterface $optionValueGenerator;
    private RequestInterface $request;

    public function __construct(
        CategoryProductResolverInterface $categoryProductResolver,
        OptionValueGeneratorInterface $optionValueGenerator,
        RequestInterface $request
    ) {
        $this->categoryProductResolver = $categoryProductResolver;
        $this->optionValueGenerator = $optionValueGenerator;
        $this->request = $request;
    }

    /**
     * Simple demo implementation - process category-based options
     */
    public function afterSave(Option $subject, Option $result): Option
    {
        // Example: Check if request contains category data
        $categoryIds = $this->request->getParam('copex_category_ids');

        if (!empty($categoryIds) && is_array($categoryIds)) {
            // Resolve products from categories
            $productIds = $this->categoryProductResolver->resolveProductIds($categoryIds);

            if (!empty($productIds)) {
                // Generate option values
                $this->optionValueGenerator->updateOptionValues(
                    (int) $result->getId(),
                    $productIds
                );
            }
        }

        return $result;
    }
}
