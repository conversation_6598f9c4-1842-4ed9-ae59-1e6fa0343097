<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Plugin;

use CopeX\MageWorxCategoryOptions\Api\CategoryProductResolverInterface;
use CopeX\MageWorxCategoryOptions\Api\OptionValueGeneratorInterface;
use CopeX\MageWorxCategoryOptions\Model\Metadata\CategorySourceConfig;
use Magento\Catalog\Model\Product\Option;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;

class OptionSaveByCategoryPlugin
{
    private CategoryProductResolverInterface $categoryProductResolver;
    private OptionValueGeneratorInterface $optionValueGenerator;
    private RequestInterface $request;
    private ResourceConnection $resourceConnection;
    private LoggerInterface $logger;

    public function __construct(
        CategoryProductResolverInterface $categoryProductResolver,
        OptionValueGeneratorInterface $optionValueGenerator,
        RequestInterface $request,
        ResourceConnection $resourceConnection,
        LoggerInterface $logger
    ) {
        $this->categoryProductResolver = $categoryProductResolver;
        $this->optionValueGenerator = $optionValueGenerator;
        $this->request = $request;
        $this->resourceConnection = $resourceConnection;
        $this->logger = $logger;
    }

    /**
     * Process category-based option values after option save
     */
    public function afterSave(Option $subject, Option $result): Option
    {
        try {
            $this->processCategoryBasedOptions($result);
        } catch (\Exception $e) {
            $this->logger->error('Error processing category-based options: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * Process category-based options
     */
    private function processCategoryBasedOptions(Option $option): void
    {
        $optionData = $this->request->getParam('product', []);
        $optionsData = $optionData['options'] ?? [];

        foreach ($optionsData as $optionIndex => $optionInfo) {
            if (!isset($optionInfo['copex_category_config'])) {
                continue;
            }

            $categoryConfig = $this->parseCategoryConfig($optionInfo['copex_category_config']);
            if (!$categoryConfig) {
                continue;
            }

            $this->generateCategoryBasedValues($option, $categoryConfig);
        }
    }

    /**
     * Parse category configuration from request
     */
    private function parseCategoryConfig($configData): ?CategorySourceConfig
    {
        if (is_string($configData)) {
            try {
                $configData = json_decode($configData, true);
            } catch (\Exception $e) {
                $this->logger->warning('Failed to parse category config JSON: ' . $e->getMessage());
                return null;
            }
        }

        if (!is_array($configData) || empty($configData['category_ids'])) {
            return null;
        }

        return CategorySourceConfig::fromArray($configData);
    }

    /**
     * Generate category-based option values
     */
    private function generateCategoryBasedValues(Option $option, CategorySourceConfig $config): void
    {
        $categoryIds = $config->getCategoryIds();
        $filters = $config->getFilters();

        // Resolve products from categories
        $productIds = $this->categoryProductResolver->resolveProductIds($categoryIds, $filters);

        if (empty($productIds)) {
            $this->logger->info('No products found for categories: ' . implode(',', $categoryIds));
            return;
        }

        // Update option values
        $this->optionValueGenerator->updateOptionValues(
            (int) $option->getId(),
            $productIds,
            $config->toArray()
        );

        // Store category configuration for future reference
        $this->storeCategoryConfiguration($option->getId(), $config);
    }

    /**
     * Store category configuration in database
     */
    private function storeCategoryConfiguration(int $optionId, CategorySourceConfig $config): void
    {
        $connection = $this->resourceConnection->getConnection();
        $tableName = $this->resourceConnection->getTableName('copex_mageworx_category_options');

        // Check if table exists, if not create it
        if (!$connection->isTableExists($tableName)) {
            $this->createCategoryOptionsTable($connection, $tableName);
        }

        // Delete existing configuration
        $connection->delete($tableName, ['option_id = ?' => $optionId]);

        // Insert new configuration
        $connection->insert($tableName, [
            'option_id' => $optionId,
            'category_ids' => json_encode($config->getCategoryIds()),
            'filters_json' => json_encode($config->getFilters()),
            'source_hash' => $config->getSourceHash(),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Create category options table if it doesn't exist
     */
    private function createCategoryOptionsTable($connection, string $tableName): void
    {
        $table = $connection->newTable($tableName)
            ->addColumn(
                'id',
                \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
                null,
                ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
                'ID'
            )
            ->addColumn(
                'option_id',
                \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
                null,
                ['unsigned' => true, 'nullable' => false],
                'Option ID'
            )
            ->addColumn(
                'category_ids',
                \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                null,
                ['nullable' => false],
                'Category IDs JSON'
            )
            ->addColumn(
                'filters_json',
                \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                null,
                ['nullable' => true],
                'Filters JSON'
            )
            ->addColumn(
                'source_hash',
                \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                32,
                ['nullable' => false],
                'Source Hash'
            )
            ->addColumn(
                'created_at',
                \Magento\Framework\DB\Ddl\Table::TYPE_TIMESTAMP,
                null,
                ['nullable' => false, 'default' => \Magento\Framework\DB\Ddl\Table::TIMESTAMP_INIT],
                'Created At'
            )
            ->addColumn(
                'updated_at',
                \Magento\Framework\DB\Ddl\Table::TYPE_TIMESTAMP,
                null,
                ['nullable' => false, 'default' => \Magento\Framework\DB\Ddl\Table::TIMESTAMP_INIT_UPDATE],
                'Updated At'
            )
            ->addIndex(
                $connection->getIndexName($tableName, ['option_id']),
                ['option_id']
            )
            ->addIndex(
                $connection->getIndexName($tableName, ['source_hash']),
                ['source_hash']
            )
            ->setComment('CopeX MageWorx Category Options Configuration');

        $connection->createTable($table);
    }
}
