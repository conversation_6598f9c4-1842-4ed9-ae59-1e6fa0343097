<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Plugin;

use CopeX\MageWorxCategoryOptions\Api\CategoryProductResolverInterface;
use CopeX\MageWorxCategoryOptions\Api\OptionValueGeneratorInterface;
use Magento\Catalog\Model\Product\Option;
use Magento\Framework\App\RequestInterface;

class OptionSaveByCategoryPlugin
{
    private CategoryProductResolverInterface $categoryProductResolver;
    private OptionValueGeneratorInterface $optionValueGenerator;
    private RequestInterface $request;

    public function __construct(
        CategoryProductResolverInterface $categoryProductResolver,
        OptionValueGeneratorInterface $optionValueGenerator,
        RequestInterface $request
    ) {
        $this->categoryProductResolver = $categoryProductResolver;
        $this->optionValueGenerator = $optionValueGenerator;
        $this->request = $request;
    }

    /**
     * Demo implementation - process category-based options
     * Looks for special option title pattern: "CATEGORY:1,2,3" where 1,2,3 are category IDs
     */
    public function afterSave(Option $subject, Option $result): Option
    {
        $title = $result->getTitle();

        // Check if option title starts with "CATEGORY:" pattern
        if (strpos($title, 'CATEGORY:') === 0) {
            $categoryPart = substr($title, 9); // Remove "CATEGORY:" prefix
            $categoryIds = array_map('intval', explode(',', $categoryPart));

            if (!empty($categoryIds)) {
                // Resolve products from categories
                $productIds = $this->categoryProductResolver->resolveProductIds($categoryIds);

                if (!empty($productIds)) {
                    // Generate option values
                    $this->optionValueGenerator->updateOptionValues(
                        (int) $result->getId(),
                        $productIds
                    );
                }
            }
        }

        return $result;
    }
}
