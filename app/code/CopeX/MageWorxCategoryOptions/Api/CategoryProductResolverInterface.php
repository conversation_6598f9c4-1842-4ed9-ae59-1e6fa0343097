<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Api;

/**
 * Interface for resolving products from categories with filters
 */
interface CategoryProductResolverInterface
{
    /**
     * Resolve product IDs from category IDs with applied filters
     *
     * @param int[] $categoryIds
     * @param array $filters
     * @return int[]
     */
    public function resolveProductIds(array $categoryIds, array $filters = []): array;

    /**
     * Get products collection from category IDs with applied filters
     *
     * @param int[] $categoryIds
     * @param array $filters
     * @return \Magento\Catalog\Model\ResourceModel\Product\Collection
     */
    public function getProductsCollection(array $categoryIds, array $filters = []): \Magento\Catalog\Model\ResourceModel\Product\Collection;
}
