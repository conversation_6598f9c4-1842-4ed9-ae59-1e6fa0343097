<?php

declare(strict_types=1);

namespace CopeX\MageWorxCategoryOptions\Api;

/**
 * Interface for generating option values from products
 */
interface OptionValueGeneratorInterface
{
    /**
     * Generate option values from product IDs
     *
     * @param int[] $productIds
     * @param int $optionId
     * @param array $config
     * @return array
     */
    public function generateOptionValues(array $productIds, int $optionId, array $config = []): array;

    /**
     * Update existing option values with new product data
     *
     * @param int $optionId
     * @param int[] $productIds
     * @param array $config
     * @return void
     */
    public function updateOptionValues(int $optionId, array $productIds, array $config = []): void;

    /**
     * Remove category-generated option values
     *
     * @param int $optionId
     * @param string $sourceHash
     * @return void
     */
    public function removeCategoryGeneratedValues(int $optionId, string $sourceHash = ''): void;
}
