a:78:{s:64:"Customweb_Payment_ExternalCheckout_AbstractContext::getContextId";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:10:"PrimaryKey";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:60:"Customweb_Payment_ExternalCheckout_AbstractContext::getState";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:73:"Customweb_Payment_ExternalCheckout_AbstractContext::getFailedErrorMessage";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:62:"Customweb_Payment_ExternalCheckout_AbstractContext::getCartUrl";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:73:"Customweb_Payment_ExternalCheckout_AbstractContext::getDefaultCheckoutUrl";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:67:"Customweb_Payment_ExternalCheckout_AbstractContext::getInvoiceItems";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:6:"object";}}}s:76:"Customweb_Payment_ExternalCheckout_AbstractContext::getOrderAmountInDecimals";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"decimal";}}}s:67:"Customweb_Payment_ExternalCheckout_AbstractContext::getCurrencyCode";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:67:"Customweb_Payment_ExternalCheckout_AbstractContext::getLanguageCode";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:75:"Customweb_Payment_ExternalCheckout_AbstractContext::getCustomerEmailAddress";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:65:"Customweb_Payment_ExternalCheckout_AbstractContext::getCustomerId";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:68:"Customweb_Payment_ExternalCheckout_AbstractContext::getTransactionId";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"integer";}}}s:70:"Customweb_Payment_ExternalCheckout_AbstractContext::getShippingAddress";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:6:"object";}}}s:69:"Customweb_Payment_ExternalCheckout_AbstractContext::getBillingAddress";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:6:"object";}}}s:73:"Customweb_Payment_ExternalCheckout_AbstractContext::getShippingMethodName";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:79:"Customweb_Payment_ExternalCheckout_AbstractContext::getPaymentMethodMachineName";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:67:"Customweb_Payment_ExternalCheckout_AbstractContext::getProviderData";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:6:"object";}}}s:64:"Customweb_Payment_ExternalCheckout_AbstractContext::getCreatedOn";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:8:"datetime";}}}s:64:"Customweb_Payment_ExternalCheckout_AbstractContext::getUpdatedOn";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:8:"datetime";}}}s:68:"Customweb_Payment_ExternalCheckout_AbstractContext::getSecurityToken";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:78:"Customweb_Payment_ExternalCheckout_AbstractContext::getSecurityTokenExpiryDate";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:8:"datetime";}}}s:79:"Customweb_Payment_ExternalCheckout_AbstractContext::getAuthenticationSuccessUrl";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:2:{s:4:"type";s:7:"varchar";s:4:"size";i:512;}}}s:81:"Customweb_Payment_ExternalCheckout_AbstractContext::getAuthenticationEmailAddress";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:68:"Customweb_Payment_ExternalCheckout_AbstractContext::getVersionNumber";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:7:"Version";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:32:"Customweb_Payment_SettingHandler";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:36:"Customweb_Payment_TransactionHandler";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:49:"Customweb_Payment_TransactionHandler::__construct";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Inject";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:5:"value";a:3:{s:1:"0";s:34:"Customweb_Database_Entity_IManager";s:1:"1";s:28:"databaseTransactionClassName";s:1:"2";s:26:"Customweb_Database_IDriver";}}}}s:41:"Customweb_Payment_Entity_AbstractDocument";a:3:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:5:"Index";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:11:"columnNames";a:1:{s:1:"0";s:13:"transactionId";}}}i:1;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Filter";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:3:{s:4:"name";s:19:"loadByTransactionId";s:5:"where";s:30:"transactionId = >transactionId";s:7:"orderBy";s:10:"documentId";}}i:2;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Filter";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:3:{s:4:"name";s:33:"loadByTransactionIdAndMachineName";s:5:"where";s:67:"transactionId LIKE >transactionId AND machineName LIKE >machineName";s:7:"orderBy";s:10:"documentId";}}}s:56:"Customweb_Payment_Entity_AbstractDocument::getDocumentId";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:10:"PrimaryKey";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:59:"Customweb_Payment_Entity_AbstractDocument::getTransactionId";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"integer";}}}s:57:"Customweb_Payment_Entity_AbstractDocument::getMachineName";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:2:{s:4:"type";s:7:"varchar";s:4:"size";s:3:"100";}}}s:50:"Customweb_Payment_Entity_AbstractDocument::getName";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:6:"object";}}}s:59:"Customweb_Payment_Entity_AbstractDocument::getFileExtension";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:55:"Customweb_Payment_Entity_AbstractDocument::getUpdatedOn";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:8:"datetime";}}}s:55:"Customweb_Payment_Entity_AbstractDocument::getCreatedOn";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:8:"datetime";}}}s:59:"Customweb_Payment_Entity_AbstractDocument::getVersionNumber";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:7:"Version";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:55:"Customweb_Payment_Entity_AbstractPaymentCustomerContext";a:2:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:5:"Index";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:2:{s:11:"columnNames";a:1:{s:1:"0";s:10:"customerId";}s:6:"unique";b:1;}}i:1;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Filter";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:3:{s:4:"name";s:16:"loadByCustomerId";s:5:"where";s:24:"customerId = >customerId";s:7:"orderBy";s:10:"customerId";}}}s:69:"Customweb_Payment_Entity_AbstractPaymentCustomerContext::getContextId";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:10:"PrimaryKey";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:70:"Customweb_Payment_Entity_AbstractPaymentCustomerContext::getCustomerId";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:68:"Customweb_Payment_Entity_AbstractPaymentCustomerContext::getStoreMap";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:2:{s:4:"name";s:14:"context_values";s:4:"type";s:6:"object";}}}s:73:"Customweb_Payment_Entity_AbstractPaymentCustomerContext::getVersionNumber";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:7:"Version";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:44:"Customweb_Payment_Entity_AbstractTransaction";a:6:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:5:"Index";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:11:"columnNames";a:1:{s:1:"0";s:21:"transactionExternalId";}}}i:1;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:5:"Index";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:11:"columnNames";a:1:{s:1:"0";s:7:"orderId";}}}i:2;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:5:"Index";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:11:"columnNames";a:1:{s:1:"0";s:9:"paymentId";}}}i:3;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Filter";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:3:{s:4:"name";s:15:"loadByPaymentId";s:5:"where";s:22:"paymentId = >paymentId";s:7:"orderBy";s:9:"paymentId";}}i:4;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Filter";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:3:{s:4:"name";s:16:"loadByExternalId";s:5:"where";s:46:"transactionExternalId = >transactionExternalId";s:7:"orderBy";s:13:"transactionId";}}i:5;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Filter";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:3:{s:4:"name";s:13:"loadByOrderId";s:5:"where";s:18:"orderId = >orderId";s:7:"orderBy";s:7:"orderId";}}}s:62:"Customweb_Payment_Entity_AbstractTransaction::getTransactionId";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:10:"PrimaryKey";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:70:"Customweb_Payment_Entity_AbstractTransaction::getTransactionExternalId";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:56:"Customweb_Payment_Entity_AbstractTransaction::getOrderId";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:64:"Customweb_Payment_Entity_AbstractTransaction::getAliasForDisplay";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:60:"Customweb_Payment_Entity_AbstractTransaction::getAliasActive";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"boolean";}}}s:67:"Customweb_Payment_Entity_AbstractTransaction::getPaymentMachineName";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:66:"Customweb_Payment_Entity_AbstractTransaction::getTransactionObject";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:2:{s:4:"type";s:12:"binaryObject";s:4:"name";s:23:"transactionObjectBinary";}}}s:76:"Customweb_Payment_Entity_AbstractTransaction::getTransactionObjectDeprecated";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:2:{s:4:"type";s:6:"object";s:4:"name";s:17:"transactionObject";}}}s:66:"Customweb_Payment_Entity_AbstractTransaction::getAuthorizationType";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:59:"Customweb_Payment_Entity_AbstractTransaction::getCustomerId";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:58:"Customweb_Payment_Entity_AbstractTransaction::getUpdatedOn";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:8:"datetime";}}}s:58:"Customweb_Payment_Entity_AbstractTransaction::getCreatedOn";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:8:"datetime";}}}s:58:"Customweb_Payment_Entity_AbstractTransaction::getPaymentId";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:58:"Customweb_Payment_Entity_AbstractTransaction::getUpdatable";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"boolean";}}}s:64:"Customweb_Payment_Entity_AbstractTransaction::getExecuteUpdateOn";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:8:"datetime";}}}s:68:"Customweb_Payment_Entity_AbstractTransaction::getAuthorizationAmount";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"decimal";}}}s:68:"Customweb_Payment_Entity_AbstractTransaction::getAuthorizationStatus";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:53:"Customweb_Payment_Entity_AbstractTransaction::getPaid";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"boolean";}}}s:57:"Customweb_Payment_Entity_AbstractTransaction::getCurrency";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:77:"Customweb_Payment_Entity_AbstractTransaction::getLastSetOrderStatusSettingKey";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"varchar";}}}s:62:"Customweb_Payment_Entity_AbstractTransaction::getVersionNumber";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:7:"Version";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:63:"Customweb_Payment_Entity_AbstractTransaction::isLiveTransaction";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:7:"boolean";}}}s:38:"Customweb_Payment_Alias_IUpdateAdapter";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:38:"Customweb_Payment_Alias_IRemoveAdapter";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:31:"Customweb_Payment_Alias_Handler";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:44:"Customweb_Payment_Alias_Handler::__construct";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Inject";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:5:"value";a:3:{s:1:"0";s:34:"Customweb_Database_Entity_IManager";s:1:"1";s:40:"Customweb_DependencyInjection_IContainer";s:1:"2";s:28:"databaseTransactionClassName";}}}}s:54:"Customweb_Payment_Endpoint_Controller_Process::process";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Action";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:5:"value";s:5:"index";}}}s:66:"Customweb_Payment_Endpoint_Controller_DelayedNotification::process";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Action";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:5:"value";s:5:"index";}}}s:64:"Customweb_Payment_Endpoint_Controller_DelayedNotification::check";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Action";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:5:"value";s:5:"check";}}}s:64:"Customweb_Payment_Endpoint_Controller_Abstract::getTransactionId";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:16:"ExtractionMethod";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:52:"Customweb_Payment_Update_ScheduledProcessor::process";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Cron";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:41:"Customweb_Payment_Update_ContainerHandler";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:54:"Customweb_Payment_Update_ContainerHandler::__construct";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Inject";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:5:"value";a:4:{s:1:"0";s:34:"Customweb_Database_Entity_IManager";s:1:"1";s:40:"Customweb_DependencyInjection_IContainer";s:1:"2";s:28:"databaseTransactionClassName";s:1:"3";s:26:"Customweb_Database_IDriver";}}}}s:68:"Customweb_Payment_Authorization_Method_AbstractFactory::setContainer";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Inject";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:46:"Customweb_Payment_Authorization_AdapterFactory";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:39:"Customweb_Payment_BackendOperation_Form";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}}