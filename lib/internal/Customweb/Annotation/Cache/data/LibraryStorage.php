a:7:{s:52:"Customweb_Storage_Backend_Database_AbstractKeyEntity";a:2:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Filter";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:2:{s:4:"name";s:17:"loadByKeyAndSpace";s:5:"where";s:36:"keySpace = >space AND keyName = >key";}}i:1;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:5:"Index";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:2:{s:11:"columnNames";a:2:{s:1:"0";s:7:"keyName";s:1:"1";s:8:"keySpace";}s:6:"unique";b:1;}}}s:62:"Customweb_Storage_Backend_Database_AbstractKeyEntity::getKeyId";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:10:"PrimaryKey";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:64:"Customweb_Storage_Backend_Database_AbstractKeyEntity::getKeyName";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:2:{s:4:"type";s:7:"varchar";s:4:"size";s:3:"165";}}}s:65:"Customweb_Storage_Backend_Database_AbstractKeyEntity::getKeySpace";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:2:{s:4:"type";s:7:"varchar";s:4:"size";s:3:"165";}}}s:65:"Customweb_Storage_Backend_Database_AbstractKeyEntity::getKeyValue";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Column";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:4:"type";s:6:"object";}}}s:34:"Customweb_Storage_Backend_Database";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:47:"Customweb_Storage_Backend_Database::__construct";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Inject";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:5:"value";a:3:{s:1:"0";s:34:"Customweb_Database_Entity_IManager";s:1:"1";s:26:"Customweb_Database_IDriver";s:1:"2";s:30:"storageDatabaseEntityClassName";}}}}}