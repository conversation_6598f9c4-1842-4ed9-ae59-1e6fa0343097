a:40:{s:32:"Customweb_PayUnity_Configuration";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:45:"Customweb_PayUnity_Configuration::__construct";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Inject";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:5:"value";a:1:{s:1:"0";s:39:"Customweb_Payment_IConfigurationAdapter";}}}}s:39:"Customweb_PayUnity_Alias_Remove_Adapter";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:35:"Customweb_PayUnity_Endpoint_Process";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:10:"Controller";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:5:"value";s:7:"process";}}}s:44:"Customweb_PayUnity_Endpoint_Process::process";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Action";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:5:"value";s:5:"index";}}}s:49:"Customweb_PayUnity_Endpoint_Process::processAlias";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Action";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:5:"value";s:5:"alias";}}}s:49:"Customweb_PayUnity_Endpoint_Process::processAsync";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Action";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:5:"value";s:5:"async";}}}s:51:"Customweb_PayUnity_Endpoint_Process::processWebhook";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Action";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:5:"value";s:7:"webhook";}}}s:33:"Customweb_PayUnity_Update_Adapter";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:47:"Customweb_PayUnity_Authorization_Server_Adapter";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:50:"Customweb_PayUnity_Authorization_Recurring_Adapter";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:47:"Customweb_PayUnity_Authorization_Widget_Adapter";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:45:"Customweb_PayUnity_Authorization_Moto_Adapter";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:36:"Customweb_PayUnity_Method_PoliMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:1:{s:1:"0";s:7:"Interac";}}}}s:38:"Customweb_PayUnity_Method_PayPalMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:1:{s:1:"0";s:6:"PayPal";}}}}s:35:"Customweb_PayUnity_Method_EpsMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:1:{s:1:"0";s:3:"Eps";}}}}s:37:"Customweb_PayUnity_Method_IdealMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:1:{s:1:"0";s:5:"Ideal";}}}}s:52:"Customweb_PayUnity_Method_Afterpay_DirectDebitMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:1:{s:1:"0";s:19:"AfterpayDirectDebit";}}}}s:49:"Customweb_PayUnity_Method_Afterpay_PayLaterMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:1:{s:1:"0";s:16:"AfterpayPayLater";}}}}s:39:"Customweb_PayUnity_Method_DefaultMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:63:"Customweb_PayUnity_Method_DefaultMethod::setGlobalConfiguration";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Inject";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:54:"Customweb_PayUnity_Method_OpenInvoice_PayolutionMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:2:{s:14:"paymentMethods";a:1:{s:1:"0";s:11:"OpenInvoice";}s:10:"processors";a:1:{s:1:"0";s:10:"Payolution";}}}}s:41:"Customweb_PayUnity_Method_PaydirektMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:1:{s:1:"0";s:9:"Paydirekt";}}}}s:50:"Customweb_PayUnity_Method_SofortUeberweisungMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:1:{s:1:"0";s:18:"SofortUeberweisung";}}}}s:42:"Customweb_PayUnity_Method_PayolutionMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:2:{s:1:"0";s:13:"PayolutionElv";s:1:"1";s:13:"PayolutionIns";}}}}s:33:"Customweb_PayUnity_Method_Factory";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:39:"Customweb_PayUnity_Method_GiroPayMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:1:{s:1:"0";s:7:"Giropay";}}}}s:45:"Customweb_PayUnity_Method_KlarnaInvoiceMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:1:{s:1:"0";s:13:"KlarnaInvoice";}}}}s:39:"Customweb_PayUnity_Method_GenericMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:1:{s:1:"0";s:7:"Generic";}}}}s:43:"Customweb_PayUnity_Method_BankPaymentMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:1:{s:1:"0";s:16:"DirectDebitsSepa";}}}}s:40:"Customweb_PayUnity_Method_PaytrailMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:1:{s:1:"0";s:8:"Paytrail";}}}}s:42:"Customweb_PayUnity_Method_PrepaymentMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:1:{s:1:"0";s:10:"Prepayment";}}}}s:42:"Customweb_PayUnity_Method_CreditCardMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:14:{s:1:"0";s:10:"CreditCard";s:1:"1";s:15:"AmericanExpress";s:1:"2";s:6:"Diners";s:1:"3";s:12:"DiscoverCard";s:1:"4";s:10:"MasterCard";s:1:"5";s:4:"Visa";s:1:"6";s:12:"VisaElectron";s:1:"7";s:7:"Maestro";s:1:"8";s:10:"CarteBleue";s:1:"9";s:7:"Dankort";s:2:"10";s:3:"Jcb";s:2:"11";s:4:"Vpay";s:2:"12";s:4:"BCMC";s:2:"13";s:13:"ChinaUnionpay";}}}}s:45:"Customweb_PayUnity_Method_DirectCaptureMethod";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:6:"Method";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:14:"paymentMethods";a:10:{s:1:"0";s:8:"Trustpay";s:1:"1";s:7:"Trustly";s:1:"2";s:6:"Alipay";s:1:"3";s:10:"Przelewy24";s:1:"4";s:6:"Tenpay";s:1:"5";s:6:"Daopay";s:1:"6";s:5:"CashU";s:1:"7";s:9:"Entercash";s:1:"8";s:6:"Yandex";s:1:"9";s:7:"OneCard";}}}}s:57:"Customweb_PayUnity_BackendOperation_Adapter_CancelAdapter";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:58:"Customweb_PayUnity_BackendOperation_Adapter_CaptureAdapter";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:57:"Customweb_PayUnity_BackendOperation_Adapter_RefundAdapter";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:46:"Customweb_PayUnity_BackendOperation_Form_Setup";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:11:"BackendForm";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:48:"Customweb_PayUnity_BackendOperation_Form_Adapter";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:4:"Bean";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:0:{}}}s:46:"Customweb_PayUnity_BackendOperation_Form_About";a:1:{i:0;O:37:"Customweb_Annotation_Cache_Annotation":2:{s:43:" Customweb_Annotation_Cache_Annotation name";s:11:"BackendForm";s:49:" Customweb_Annotation_Cache_Annotation parameters";a:1:{s:5:"value";i:-100;}}}}