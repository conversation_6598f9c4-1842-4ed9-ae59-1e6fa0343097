<?php



class Customweb_Annotation_AnnotationsCollection
{
    private
        $annotations;

    public function __construct($annotations)
    {
        $this->annotations = $annotations;
    }

    public function hasAnnotation($class)
    {
        $class = Customweb_Annotation_Util::resolveClassName($class);
        
        return isset($this->annotations[$class]);
    }

    public function getAnnotation($class)
    {
        $class = Customweb_Annotation_Util::resolveClassName($class);
        
        return isset($this->annotations[$class]) ? end($this->annotations[$class]) : false;
    }

    public function getAnnotations()
    {
        $result = array();
        
        foreach($this->annotations as $instances)
        {
            $result[] = end($instances);
        }
        
        return $result;
    }

    public function getAllAnnotations($restriction = false)
    {
        $restriction = Customweb_Annotation_Util::resolveClassName($restriction);
        $result = array();
        
        foreach($this->annotations as $class => $instances)
        {
            if(!$restriction || $restriction == $class)
            {
                $result = array_merge($result, $instances);
            }
        }
        
        return $result;
    }
}
