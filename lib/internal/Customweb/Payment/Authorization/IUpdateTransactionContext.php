<?php 
/**
  * You are allowed to use this API in your web application.
 *
 * Copyright (C) 2018 by customweb GmbH
 *
 * This program is licenced under the customweb software licence. With the
 * purchase or the installation of the software in your application you
 * accept the licence agreement. The allowed usage is outlined in the
 * customweb software licence which can be found under
 * http://www.sellxed.com/en/software-license-agreement
 *
 * Any modification or distribution is strictly forbidden. The license
 * grants you the installation in one application. For multiuse you will need
 * to purchase further licences at http://www.sellxed.com/shop.
 *
 * See the customweb software licence agreement for more details.
 *
 */

/**
 * 
 * <AUTHOR>
 * @deprecated
 */
interface Customweb_Payment_Authorization_IUpdateTransactionContext {
	
	/**
	 * The URL to which a PUSH update should be sent, when the payment is processed
	 * by the payment service provider. The implementator of the interface has to make sure that
	 * the update URL can be called at any time after the authorization of the payment.
	 *       					   	 	 	  
	 * @deprecated Use instead the method from the INotificationTransactionContext
	 * @return String The URL on which a notification is sent.
	 */
// 	public function getUpdateUrl();
	
}