<?php

/**
 *  * You are allowed to use this API in your web application.
 *
 * Copyright (C) 2018 by customweb GmbH
 *
 * This program is licenced under the customweb software licence. With the
 * purchase or the installation of the software in your application you
 * accept the licence agreement. The allowed usage is outlined in the
 * customweb software licence which can be found under
 * http://www.sellxed.com/en/software-license-agreement
 *
 * Any modification or distribution is strictly forbidden. The license
 * grants you the installation in one application. For multiuse you will need
 * to purchase further licences at http://www.sellxed.com/shop.
 *
 * See the customweb software licence agreement for more details.
 *
 */



/**
 * 
 * @Method()
 */
class Customweb_PayUnity_Method_DefaultMethod extends Customweb_Payment_Authorization_AbstractPaymentMethodWrapper {
	/**
	 * This map contains all supported payment methods.
	 *
	 * @var array
	 */
	protected static $paymentMapping = array(
		'generic' => array(
			'machine_name' => 'Generic',
 			'method_name' => 'Generic',
 			'parameters' => array(
			),
 			'not_supported_features' => array(
				0 => 'HiddenAuthorization',
 				1 => 'ServerAuthorization',
 				2 => 'Moto',
 			),
 			'image_color' => 'data:image/png;base64,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',
 			'image_grey' => 'data:image/png;base64,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',
 		),
 		'americanexpress' => array(
			'machine_name' => 'AmericanExpress',
 			'method_name' => 'American Express',
 			'parameters' => array(
				'method' => 'CC',
 				'brand' => 'AMEX',
 			),
 			'not_supported_features' => array(
				0 => 'ServerAuthorization',
 			),
 			'credit_card_information' => array(
				'issuer_identification_number_prefixes' => array(
					0 => '34',
 					1 => '37',
 				),
 				'lengths' => array(
					0 => '14',
 					1 => '15',
 				),
 				'validators' => array(
					0 => 'LuhnAlgorithm',
 				),
 				'name' => 'American Express',
 				'cvv_length' => '4',
 				'cvv_required' => 'true',
 			),
 			'image_color' => 'data:image/png;base64,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',
 			'image_grey' => 'data:image/png;base64,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',
 		),
 		'cartebleue' => array(
			'machine_name' => 'CarteBleue',
 			'method_name' => 'Carte Bleue',
 			'parameters' => array(
				'method' => 'DC',
 				'brand' => 'CARTEBLEUE',
 			),
 			'not_supported_features' => array(
				0 => 'ServerAuthorization',
 			),
 			'credit_card_information' => array(
				'issuer_identification_number_prefixes' => array(
					0 => '497',
 				),
 				'lengths' => array(
					0 => '12',
 					1 => '13',
 					2 => '14',
 					3 => '15',
 					4 => '16',
 					5 => '17',
 					6 => '18',
 					7 => '19',
 				),
 				'validators' => array(
					0 => 'LuhnAlgorithm',
 				),
 				'name' => 'Carte Bleue',
 				'cvv_length' => '3',
 				'cvv_required' => 'true',
 			),
 			'image_color' => 'data:image/png;base64,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',
 			'image_grey' => 'data:image/png;base64,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',
 		),
 		'dankort' => array(
			'machine_name' => 'Dankort',
 			'method_name' => 'Dankort',
 			'parameters' => array(
				'method' => 'DC',
 				'brand' => 'DANKORT',
 			),
 			'not_supported_features' => array(
				0 => 'ServerAuthorization',
 			),
 			'credit_card_information' => array(
				'issuer_identification_number_prefixes' => array(
					0 => '5019',
 					1 => '4571',
 				),
 				'lengths' => array(
					0 => '16',
 				),
 				'validators' => array(
					0 => 'LuhnAlgorithm',
 				),
 				'name' => 'Dankort',
 				'cvv_length' => '3',
 				'cvv_required' => 'true',
 			),
 			'image_color' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAAAyCAYAAADY+hwIAAAELElEQVR42u2cLWzbQBSAAwoGBgY2aWrs+BSfq0odKBgoKIg0MFAwMFgwUFgwUFBQUFAwEFBYEDAwUFAwMFAwMDBQMDgQMFBQUKlJGmkBAQHZe+ez62Rnx7673JzsnvSUtI2d8+d37+/OrVSsmJFGpbFCPW+vXiPHVour73lH7L3n7fwFlxDyxK+RM3hdt6amJvVa7RXAbqLBxr+kNXKKkC0ePRIQsgmQD9gPG882HqNpWyx6BYz2Azfp+rbQb1hRErDgk5A0IQ3UPAd1HNroObQF+rNX9a+NqkOHoGNDOkh874hp+P5Hzwmad45PZ/ti7hXyAL4j5Dmc/NLgBZZdAXrwXgvg7uraOpzwxkIVaksJ8DUhj5g7sCBTtevSfWnAXdc/shBnah8NUQqwdQ25da8w4H412LTg8roJ/6ww4J5LX5cucsuneKOM83Y0pJCtwoDvoMLrk3US6vzKaEwB76HYuXfoYc/1v6bBgL//kv0OOP5WCLcafMfrVBo/HA/jPlDKg00KJvFgFR+nQQPgbzLnG1caKymWe4M3V9U44DxXorGVFnAkaNWsolIEjDNQVKlhjNEAt502ttIDZpBd/0U0vbsO/SLlHqp0S+BudnXBXWjAE8EW3IbS8bHf9U90wl14wKEVBudZZWmWdKv+uzidglkwTjbCNcBdCsDcVRzLWbB/wEG0VTIGli1A1pGS4ZgDHFkIvmKAUY3UMeRqsCNn/f4J+vE8LUYZuMYBd9z6S3HOiQl50FSZopI5cAszEhWDgXNcZBVBZgGHTfn0isylp2b9N3VUjge//WlWlWkUcDKopCm6DlOAf6+uPVWcAcelAoyd/swBgaswnYGouCXmIsISviyAM+94HxN/wz54iH1tDbnvbSkAY9MGv5BF3bDThCsil3iRsk0jzABkp3rUiVMtjXlsGc0N8Hg8Nhr9p6qxUwXLi0C0RSsPRY3nn1vwPNqZ2PnS0+gJmhpm6OelAsxal5KBka+IT8DAqa50w7HoWJZeBFZv/CKutOXkcLNUFxD4jRssNGDeZhyo9IJTix7JzpygSzdaSMDccgeqQBIzQKDBW+WiyqX7CwUYy1q+XKSlvJ5RVXZ0NKJwbKUGjP4QrYk3VEYpy+JHcwDMLA+bUzosuTBg1oMNIzha1AUORrdmVUfTq7+zxsJuUPSZWNNbjFPaVryWw8KARWtZVjVuPOEb/4YW4Jy2TkWdKAtvpg5FfZJcgHmpaa04O1AeitjlziJ4hB9ZmMLge57GrVCaxiH3LdTERkTIf7O6c4XzYEzKMVpOVFj/n3ZwETVP7ixdaOASCubIWOdjI/xhB+Zya9FlpyTgLdA3FStaJX4QEZ/0jB+as6JN8HnlCdpUcf+AlaR7qG9Tz3voT6AV4wPhdYUdMFY4XM/bTX32G32x/d8PaooxLcn0D9vu9ESGJGE3AAAAAElFTkSuQmCC',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAAAyEAAAAACtAZ+XAAADrUlEQVR42tWaX0jbQBzHxTd9sj6MIrIpkzL6qEWRCQPn9tLnPhWCIIogLg+zIqxdoRD/4ZylMigr0q4qrIIUBBGEEcoKEoV2E4rFIEhRK0WoSEcRS+c1y5JUcxdDWpP7QR96yS+fXu73+33vrjVFjbUa8HEz+P3tQqd6bbFpofPH0//Al1cfXtPHah/Zn38mG28GS8Cu8csrLUyGxPOvz26Bc9hCp1bm74z9Fni3h50d6m+fntwC7zTvNN8z/H6fcUKPk/Ks/9waebgN2MG9mAWz4KTj1ep0OlROBebCPcDZwGyrnAcqbZhla14C8GndKKUGXMZ8RgTwdXRCrx5cYNsdUOAwrS5ca2So4ToKAVbTdGCNfCkKfNykPlxrZOlAFPj3O6XiG5b0MAv/2uEgKklygXcHOK/LxDPxXPIhyTwbSK5sOKbO+BjvO2B3jHRxV7rq8zq4/7xusw2Rh+W1dMhrYKEJk/h1BTOHO0plA6jhcO5z3hQFBi25MmBHAWfiXGU7bkLh2gi+N8WBi8UUBV74nFP8isM9Fjj6UQpuhYGZwPUa4P3A1nql4VYcuFhcbBEWVGGL4ABizlkwS8OtAnCKWm8X791ss0ZsBDw35HWuei40ZQKDESmYM3FUXIMWC4r3rfWOdN0VjuK4soGPxjgXOLk6DX+l4s1nTK7AB8ZdpmdkAif8QjfL3fKAL07g/V9+lddNmcBMsPAtE5cDfPUC3r/erhDw1rzQDU7KzSHwyVQwT50pAiz85UMNh3vygPvPwzQqA/PVhmzgDQdhctXj5IR+tjVMwwRSOgR77UDLoUpyws+XUhXPw7BwzAYAgo3gVhFiw1M14GxglEJLn9VplJ/PjioBew2wcDytYzESfpQOroqWiAWtEee+lGyOk6iFwmlduVhVHPhwDzwCpob55Qem6VhlB4KvYsCxIDMiMBDwBjijPCif2x0VAr448RqkFG1hvRwOoqXUcrfCwLkk5XHT/LwJKwzlBZ4wHY2hR1kUOEV5DV6DmyZMUk1Yk9iVcLkXNw2+ASYUjozZCPgzNhyiwNx6SyMbKddReTu7j7ZVBbSU2nD7zzllcg9wJq62MeZmsEiWoDzCva/HtcUWCUcGlGeoQR1HBsvdQlUnmoezgaUDpmo9lg0Hfca7GRpaOArmFJXwp0NgP7PaJraIKgHH9Nu1mjpYzGHguE4bbbKxdNY8Yz97owXc3Z5vfSXgHOYa3+1RO25Yx5yJ1/xTRLVq/r8EsJieIf0LVHJcynbx+GcAAAAASUVORK5CYII=',
 		),
 		'diners' => array(
			'machine_name' => 'Diners',
 			'method_name' => 'Diners Club',
 			'parameters' => array(
				'method' => 'CC',
 				'brand' => 'DINERS',
 			),
 			'not_supported_features' => array(
				0 => 'ServerAuthorization',
 			),
 			'credit_card_information' => array(
				'issuer_identification_number_prefixes' => array(
					0 => '300',
 					1 => '301',
 					2 => '302',
 					3 => '303',
 					4 => '304',
 					5 => '305',
 					6 => '309',
 					7 => '36',
 					8 => '38',
 					9 => '39',
 				),
 				'lengths' => array(
					0 => '16',
 					1 => '14',
 				),
 				'validators' => array(
					0 => 'LuhnAlgorithm',
 				),
 				'name' => 'Diners Club',
 				'cvv_length' => '3',
 				'cvv_required' => 'true',
 			),
 			'image_color' => 'data:image/png;base64,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',
 			'image_grey' => 'data:image/png;base64,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',
 		),
 		'directdebitssepa' => array(
			'machine_name' => 'DirectDebitsSepa',
 			'method_name' => 'Sepa Direct Debits',
 			'parameters' => array(
				'method' => 'DD',
 				'brand' => 'DIRECTDEBIT_SEPA',
 			),
 			'not_supported_features' => array(
				0 => 'ServerAuthorization',
 			),
 			'image_color' => 'data:image/png;base64,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',
 			'image_grey' => 'data:image/png;base64,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',
 		),
 		'discovercard' => array(
			'machine_name' => 'DiscoverCard',
 			'method_name' => 'Discover Card',
 			'parameters' => array(
				'method' => 'CC',
 				'brand' => 'DISCOVER',
 			),
 			'not_supported_features' => array(
				0 => 'ServerAuthorization',
 			),
 			'credit_card_information' => array(
				'issuer_identification_number_prefixes' => array(
					0 => '6011',
 					1 => '622126',
 					2 => '622127',
 					3 => '622128',
 					4 => '622129',
 					5 => '62213',
 					6 => '62214',
 					7 => '62215',
 					8 => '62216',
 					9 => '62217',
 					10 => '62218',
 					11 => '62219',
 					12 => '6222',
 					13 => '6223',
 					14 => '6224',
 					15 => '6225',
 					16 => '6226',
 					17 => '6227',
 					18 => '6228',
 					19 => '62290',
 					20 => '62291',
 					21 => '622920',
 					22 => '622921',
 					23 => '622922',
 					24 => '622923',
 					25 => '622924',
 					26 => '622925',
 					27 => '644',
 					28 => '645',
 					29 => '646',
 					30 => '647',
 					31 => '648',
 					32 => '649',
 					33 => '65',
 				),
 				'lengths' => array(
					0 => '16',
 				),
 				'validators' => array(
					0 => 'LuhnAlgorithm',
 				),
 				'name' => 'Discover Card',
 				'cvv_length' => '3',
 				'cvv_required' => 'true',
 			),
 			'image_color' => 'data:image/png;base64,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',
 			'image_grey' => 'data:image/png;base64,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',
 		),
 		'eps' => array(
			'machine_name' => 'Eps',
 			'method_name' => 'EPS',
 			'parameters' => array(
				'method' => 'OT',
 				'brand' => 'EPS',
 			),
 			'not_supported_features' => array(
				0 => 'AliasManager',
 				1 => 'HiddenAuthorization',
 				2 => 'ServerAuthorization',
 				3 => 'Recurring',
 				4 => 'Moto',
 			),
 			'image_color' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAE0AAAAyCAYAAAAZfVakAAAFEUlEQVR42u2aL2zbWBzHCw7kLslUsJMKTrpJN3BgYGBgYKDSgYGBgoIDA3H8ntNTCwaqq50NNFLBwEBBwUDBwMBAQUHBwEDA7pLpelJBwcBAQUFBQUHBQED3+z7b1+eXZ8eO7dyWvJ/0U9L62en75Pf/dW7OiBEjRowYySLduU6lV2+zXtU76NW8417NPSLdw+/o2neGkCK96tPfCNQp6VWMfvrrh417hlQggEFQBgnAQr38UHcfGJf8sVODFUXgVL2Tv2veLlRjfadw49l2y5q7pkB5KUMB1H7dex2F6j6ZdWj7ctzSBfzAGq8tru69nWlo5ILnErSduHX9WvuN7L4zbmnXbteveZuxcKveKwPtGtpAgvYsYd2O7MYz7p5P7xCsRShiV2yWne/Mh+t6N7zbplAzkl76896tcfXD9xs/zRYsCvio7lN0ACPUvaAEsT79LdONjfv5YUWVypblKbcyqd7SK6Yb28ikUHQIQ23WsMXtJ31mq9W62eT8d9JnUNt2HnPOU7u3xfmixRzXsvlmolrWfEnTDO9Et3EC1IcVJrj0Yq/ePowB19Xdwxi7bTNnv8n4lU5txt/SmjtxnwmwTZsfxN2vqmX9cWuC0NztNPMyrAma+JHQyKKWaCOXKTY7aDLWGLIuy6oQsI9pgU0UGiAU4OJd1Z18GOk3DMgR6IxtZbp/YtDovTrmgTX1q95jwCQ4W7r5GQpduv9MB01YCONnmk19shh7Ra+7MRZ0Icck+vlIuX72lcQ0d0Xjfu817runui9KDR00m/P1YSBsq9OJ3k/r1lRrpI0/l6BFr5H1/n+9pgTt3/r6zQiIuruExlyrdE1ei+JWB4022I9slqwr7m9pMueFao3Scy4i14R1soaqlImXkXAmBe20gGb/TAMtGmcc525iKTIUl3wXE26cLaYdJX1WIdDghgUMMI9GQVPdctjaovEvDOYB0POM4D6XAu4/SytgJgZYo6AlFq1+0ojELrnotW37XtayAxY36ovKFdPUzIlM2a+2G2mVnvExDzTRJUTXX6prVldXa7bNGcW/Hbre1ejpkIsXnTSSsuc/1T8XJBBZNDM0q9X6VXU/m7E3WfeDGEj3HivZulFmcXusXkdGBcyggO2mU3c7NhGgb5TU7xRE1vw81FaRO/pu2Xqg3henop9VXJyy6aNyO4KCRztZq3gJ7kup/brKoQMkkdJ7zyJHO2NtlPN3CN6IX+RaJ/mgOS8mNeUYwOKK+CeXrFaBLgDARGwieLmA2fwAGbmM06fjpH9ywQwNIyKMtMuBBkti79FahZW8KC3Gt7Dz4HmNubJEOYorSN21cUqOb0b87DhWWaHXevtQduuphBZMMyoY/+CAJZfSM9QCeWqhlSkGWonQ0FijOS9tcDht0JDpUJOh1KD3K+gt83wmwJc26v5aoIXVP6wNwNBzogShzS9g83iPaQetu486ruk4D324zrK4h/M1vGKdKIipi6DXBVwPjgqX/NESa1BZ8zOa/fCLwXMwGLWY8+TbgmbzTWyWoGz7kPghajbAxO9Q1QswtLHwiC/oV3cBEeem9P41oPnvBfAKRu0YBATnBRU8TwAX0xS2QiB/8e/D+UMJnUMOaJHRjW6N3y7Rt09gwimt+PZFnCNro00HBe+KBPo5wGLT4ZQE68TZKlkRrFJAg3XCXQHfP5hexs9Yg3Agxkw23yu1GJ4mEbM5AgvrNDQySOFTXSNGjExKvgAUDcNzmIs00gAAAABJRU5ErkJggg==',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAE0AAAAyEAAAAABshtU7AAAEfklEQVR42u2YX0gbSRzHe3APpbSV43z00YMrwj2Ug9am93Av5SilhJ4P1aRynITSXtcikV5ylZCkd1itYsRwZetuEaIQYsoaTkRpCUkgxooklJRiLBK6hGr+oD2Txbhh67k3TncTd7NJat0U3O/T/GZm57Pzm5nfb+fIdtU+Rw7RDtHkRqMbnx398/t2S8e53pPPjjJ1VYP2YrqtR/kDpxsnXrurAu21+8p7Phirq+ZXMdnRNpM3TgAcTcBKW2k4f209dKPMaFOnAcrDJEDZTFrOAMs/38mM1n0JrC5u6W8mwcyZf5IZ7ZqbxXj0hG/rPwUcLDMacJ4d49sGI1WBBnbnuJpve/QEOFlmtDf2MBWmNpN8G3WMta00HwYqYbTEhWJKPZAJzY5dNRdGgUKpvp1oOnC0iF4KC2p24oDRwNnFqd2Ct46rx9UPPwQuqO5L+S9Jp+fRSdOkKRBdWxUaZHF0CnUp80XlykDTBLjBf/8qouc3C1OdNq62i7dT46RV9+tbTgO62Ba/59rq4GV+PVSqtiI0vHVvfsbUWem9aCHzzYuFg2rUfuRD1sfcPS4EViGalRZrDF0O0RZHNWrhgUNm0IJQCNdXhKYJwNSHqfM2WOmxBJenUcd+CXBoNNNBwIH0X+NzIyZuhm55wVoy7jq7g9iHtTadgWB6NXRg70no4IkmDm2mD4IQCmb3HW4tnEen6v937pYWRz9qhwK0jRZQeo4ORjg9R4E19YBD+0sDhsXn+C90IHAe2dItLyjdPe5HoBaCcbICtLYeqQ6sSwEanDMSyz9IoJ112YhJeKUZdfm9SkDTq6U6dJwrRGMKWxDcQk+nb58VhrseloIrQJPOyLqaC9EKfhIZuL7A8RtdEjs+jDqmvLUG9+ermFsrpN+CxdHmUWC9eRFashlfbMzY+zNU5zewZ/HNIbpD1/9gIYQljrbyGDoQPS82JJUzfAHacEdzSWjtFljeaJnO9J/qat4rvJWPtjgKFDI7kOthaI0u7fzPfgnr+Jo0QZe/WC8zGpSa9Iid86xs9WwQK9aCDWjpdNkxtLSkR3zQ/kZmZ4XduVcczYFUEN6vvJ9okr6AEZsLp4rZWU/9jcXBBi/TTMlo7Zb8C5hxdUSfuFA62p173bOEgj3po0vFZuz22e5ZP1JWNAA/dKVo6rT44fFJstyNFvHjgq9OG3D2AaKx133eBjtWXN4GeCgfKFp5z2eGRmKpWukkUQY0P5LNMNseg0/i/pLKSaff+4zGxgAS88XQ84Ti3VaqllCsrS4POFUvcY9hIUhibi2JEYpsJmT+N7EQnEdD5nTaj6SoMSP7MR4DPvf0/idCcymzGXtNqta8Hidt9fYaB+LWPr3P/vw5kBGTUxWIDg8RikCUUNDMTN/KY5eSZmz1L/F51GNIJIaHjDp+hPgINJjkcJZsZszo1rK5LT5HYu+2XMroksfA1jhVcXJ4iM1MXMo46YstD8z0LQ9QOXvNpGkhSOV8MWbbF/v7R//+oO3vk83M9AWiVXqJxXzG92uHaCU8/wG5e24aqL/T8wAAAABJRU5ErkJggg==',
 		),
 		'giropay' => array(
			'machine_name' => 'Giropay',
 			'method_name' => 'giropay',
 			'parameters' => array(
				'method' => 'OT',
 				'brand' => 'GIROPAY',
 			),
 			'not_supported_features' => array(
				0 => 'AliasManager',
 				1 => 'ServerAuthorization',
 				2 => 'Recurring',
 			),
 			'image_color' => 'data:image/png;base64,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',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGwAAAAyEAAAAADMGb3VAAAFZklEQVR42u2afUhbVxTA0+6PUl3APx6i4MShmEJhMQvNIA4Z7XAITRO6iRnatZChaRREMyZ0MjGtmZsQk4UOP+owI47aGbCuzmIrvlZnowOhkciK6AyhBSE4GaKpRLLdnpy8fGoiZu1L884f75373k3u752P+/Eux5ukB+e1AXvyC60eeMguuftg/l3Pqahgbun3DSc7uRQ7Je/tptG19AhgywbhO2yFYuBodQjY04L8DbZjEclwTn0cBFbWAzd46zeNm9pEBrX2eGLRePTGfT/Y3QdQWGrYmEh0tko0GJf6zuYH+3yAFGTpHFWJT8OJB+Nf9IPxL5ICRd//0b8kHoxLEWd8AUaNEPW6KlnAHBd8YKCaLSmwFBjLwBaKiPz5OOnAJHIiCjoFxhYwfSeRH39KJY9XC2z+THtx/dH24vGhbZ1zC6zk3Aq1mNUEGnm+ZbZOMGglpbt8Ut5UUSdoU5nUK+WhYEKl2UKk1JCx0rh6r9+WbbYo+gImICU11f3OKdmyce7ycGGthJompadvQa1v/TMRRR+UiHdiBjOpIZKINFX80QhXC0WhMfbzNmiLHNkJciaIzq36o0xtiVx2Qt+5rQsEK5sGrVYyXMj8p9mSsQJt0R4PfslPC3nrXCqnwi0FnWhEngwRbWMCwGMAs5oCGyaRf5a7H9ilv+Hca9vW1QmwXvkc83J2+eFga28GA3w5A235+lOv17Voy7Zl+/1HGNhGeI7fClq3MGZX/EKLQCPiRU5HOjYvOhiRNtWIeKGo1wZancBq8npXyvGljA+Fg/03czfWZ5Ya7vUjKLx78U6ZzwZZumUj3CtxMPUmtYhPjvcEMYOBW0nkzx6BjvaIDibvgfjzetFeDp+TjQ+B3uwOB3NLC45B89HJZGpsnlBZU924Wp85dAHuQAyC83lEeSVcakbD2DJGMHQv1LuP7AfWa4Mnt3UIinWfPQp8PhhsRoONmfTN22uqiXZ2MHxuCHeu6DE6eetwVZ8ZN9j55xjy+4ONLfmcqQv0ynP4W1hSrQ8Hs2VjYzCJVLq4lEztEUFSmNSaLcHIeSVg20ltrYScN7VZujjA0BXpFyCbE/snj4lmrIsJA914igN6e3GkGBMqiU5NYxohkQQtcUv5rYHzRADjUuCaHtG8kJxveOLqxzTdaLNem/lDec/+yYMB03dCiaab2NtqUtB7JQ+7U55fNg3RgsnD7mSuSxyIjGBMXXKcvhUXmKMQbRactmMBW+uqPIevBaEk8pbZSP1Y6AFp/KYRey9iFXBLBoxLYZ4kryXukccUh+mDWmZH02IH83qXrgV30KQX+yctEtgdLtoqsIMWKpklQI+o0hXcxzFp3uttXD3AkMqlHE37QWC8RBft8nEc4lJGG1IthnwFoIs60hsWFHS1viPdaoLOORzMbMnSGa7OaBxVc5drJQErTa39TrvTUTWj+eR90jIiJK2AFBwDK7qlORVxg611BV5DlJ1/fliDYAbsIIPcs4N7194TTN+poHttY0ujad1HMHm0qV42WKXrhgfXqj0iyKhxgl3hBUcJ6XLBEV8mWODg+LrqQNMWZnwI0rCwdO3w5mMHBet34u8YruJM4ADJY/7MRPOgdWzp98fY2R4WWE5F2TSRaM4UTcj4UfNBrWTveqnltxTYqwaW4UxSMPikjjMd9oO57D6wql9hpuNaTAYwHu3/8Gf5CIrk+TiSZjPYV2Y/mOeU+DcoFO9MyXD1gZ1gOSLY7eEbk9tXs/yrDxkrJ8cSJ5HH44cnt3NDNrDMqdi/04MaGXgYYcvRWrp6C75Gs3Prinzevhp1k5hben/Y9Nc3b7BLet66nUtS/Ou5rS9Zjn8BMZm8Nb5SG3QAAAAASUVORK5CYII=',
 		),
 		'ideal' => array(
			'machine_name' => 'IDeal',
 			'method_name' => 'iDEAL',
 			'parameters' => array(
				'method' => 'OT',
 				'brand' => 'IDEAL',
 			),
 			'not_supported_features' => array(
				0 => 'AliasManager',
 				1 => 'HiddenAuthorization',
 				2 => 'ServerAuthorization',
 				3 => 'Recurring',
 				4 => 'Moto',
 			),
 			'image_color' => 'data:image/png;base64,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',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADkAAAAyEAAAAACTJPDZAAAESUlEQVR42sWYXUgcVxSAg0P6lqcGLAQtDXlYCCGQh9Q8rPRBmJfBC4OyVSIskUW3MWXFOiiB/G2yxVbDClvT20INC6us7UJCgikVZou1stmGFCosSdl0E/Qimj8ilbQh9naOZyc7M46irjO552V/5tzvnp977pm7i7s+dr015Ny+7cjrU2UgPyDbk4PMSxpZK+sWBqh6+d93XECaxUP8wog0t2/TyK276HVwls/yjDQpxWibcIzp6Hr2q+QQ0jqeBVOSLCD2E/qwzwUkjvvt4QrPqpsHqF2CbRp5+vu1EikMTg9Oj6V/+Slf/fIH49N53iSgrWuhm0YSZWNpWDzzZerqAitpxOlBZgfdMSSK7OtL/3Vd1/kt6LGxdIeRKLHEq37UuiUd0CyNUceRRAlNze9GvZsSJJJxrzqEJIq/So/rcc1Ov+ACkihdPnRvbtW5k1+4gCTKlUuo261tmE66AXI5GB1vZfXsrJQzlK3RoXhHpNCchKmak6NDugSiesrAt/Mn8VukMDoUKcg+dO7DIMRz+bN1kDnJ+6ZMH2DfUrOtT3p6K4kSiBpLBCB8WXRivhqRqsi5KhJl+B4+9ZHm2pRki/yH11lOh0zxwZlsvhqhDYuIvF87k53JhqYA0ZfWl3BixYhsTuJSzkqwP22RSWo9kI6zUiwDUShr/TIieysD0UBU9umIafqkh/NYwogkyu1D8OykhhSZLRJWY5bDxJg+uRaIqtGxGMulGoBNtHF+96kZOZZG78FcGE0LsoeuPfeNyEeDnKeuWpG9las1tePHzzl/1e/LGpG6y6H0/TFkg4yvQTaaHAufrlyyOnYsbUyxSMGIPDWHv0JS4t60IJeDXgtSLaaPKt7ZA6mwVNOcROQ0VUVVBADYvlSzwBYYxHqiDZHzu+F/+AQDju7UsO0m+V06wkrAAcsm+ftO+D/zJtEjq3wINn3zHufPP0akebS+2SY2peBZMEJlQWSdNGMoBap4491Ywl8FE7fU4vrRyt5KVQTL0PXw24mVWKL0BOq3aVYmqeMFD09Q1Icu4ebXriD1IED9yVy0RX4qeIlR5oPlIbuKVkKGPNhvi2wSzBk7W6aV/TJ2vDDX472uIKE8wD4o1THHkXh8RcfXLes7jQxNoXajFskRyRXkz39iTYNWRG+5HEW2h1beX63K46Wjy1Gk7JvJguYLfpjo9dVhJOYq5wMUbCx17I4h4x36CxGclBODG/SxO4Fsqb37FLUW+VFtjiZhwxeEcpENi7EE9ECYqdC81ZGlPY4h20PXzkEXpLu0XtsaR4n17qBMpL8qEO3ynT+ZkG4fKsGwpYEYHmE5xYUXd84zEi7cL2AhdxT5gn83Xle8kIm/dPR6Is+vSeGKeuYp9r7hivXuQ0zIbmFr0kmbhEbmJceYMfYiGxnRX3gcvd3yEFkIV9z6av7Cpi/UUsPbE/Vy5uKD/Vu7QHx7l6Nujv8BicIW6mI4150AAAAASUVORK5CYII=',
 		),
 		'jcb' => array(
			'machine_name' => 'Jcb',
 			'method_name' => 'JCB',
 			'parameters' => array(
				'method' => 'CC',
 				'brand' => 'JCB',
 			),
 			'not_supported_features' => array(
				0 => 'ServerAuthorization',
 			),
 			'credit_card_information' => array(
				'issuer_identification_number_prefixes' => array(
					0 => '3528',
 					1 => '3529',
 					2 => '353',
 					3 => '354',
 					4 => '355',
 					5 => '356',
 					6 => '357',
 					7 => '358',
 				),
 				'lengths' => array(
					0 => '16',
 				),
 				'validators' => array(
					0 => 'LuhnAlgorithm',
 				),
 				'name' => 'JCB',
 				'cvv_length' => '3',
 				'cvv_required' => 'true',
 			),
 			'image_color' => 'data:image/png;base64,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',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEEAAAAyEAAAAAB2ujW1AAAEQUlEQVR42mP4P+CAYdQJ2JxwXWPZvH63dpNO5i757nc9xX3O/dUT9SZJTZG8IY+s7vKh1XaTp3Xr90h0X+zZ1TOht6p3bp9ln33f7cdrkdXd3Ll16eIXC3Yt3rTk6LI9Ky6sOr5m37pLG55vuvdWE4sTrvHGbTRfaaVr02in6LDU6Y5rvDuDZ4j3DT/+ANegkwfWw9VppCY5HXe+4HzT5ZHrOdcvbg/c2dw/efzxZPE86LX2UilM3dW35U2xzxN1UkLSq7Ot8kQKbUqSy7urUutUGjNbJjz8iOGEPRPMck2LCDvh4H2XbMfbhJ2wwz1qXjQvCU64vc88zdiasBOu7nY+47iVsBOuqIWXhz8lyQnpXAbbiXFC1nNHMWKcUOQUso8kJzzK1Rcnxgn3JjksJsYJ1yYFTiPRCTuciHPCjhzinLB5OslOWLKOOCcsYCHOCStkSHbCtGejThh1wqgTKHNCdF3+8eKWktySu2mfgnpp4oRG6xddLz695H+57OXha88QTogXWyJ4rfSvJ2ol/7HiksKGpKj1ECd8lXll8arl1cXXGa+3vvF5U/3mxNv4t2teZl2Zu0idBCd0IbUPXkVBnLCmdHc+vuZHeSByZY0dnP1CkROQwYc78xXSf4VEBp5I/tTIuenT66OYTmiJbHve8bvbfgXjT1aE6IxvVHHCI//QONTk6P2nKSMtGdUJiLSwuQYhuu4SVZyQNoNwqwnZCeveUTkUrpUS03BDRMQ8vy+RMLELtVRJCzv5iHMCOniYuOp4jfWAOuH//xcxG2Wp4oQLIcQ5AZYWMp8jJ8fDBUQ6Yb4YUm+gCdUJfz1jtUlLjtlWX5bBRP9pdU/EcMJRdc9yVCd4XnrRhTBqtSJ6jrh8yOcjuhOydRPW43JCyYLfhQjx+TOwtKD/nD1Z2nYlYobVbZATsm/c+IHQ8GV3ohxm0fRodvv+gByQE3y8Mk/PYbyhiLtomjLrzkOE6O997XOxdmXgpd4utNL/avkdRPP1Miuq7HedL9qkFdD//295iSUtXNx/6Ov7F5iKn/Esb4piQq0py4X3RHy+g6n29dHjc9EjAh18F7pTPf8OnhzhbBq3saCq5X6bRLtJ68Wi2BA73JV11MGiQ12lkG5tHXPm6eCq0VbTqBNGnUBnJxxdM+BOAA13EeOEW3eIcwLacBdhJ4R6fM0nxgkBib98iHFCnxLaoB9hJ/Swg9QRdsLE2yB1hJ2waxaJTogofd5PjBNCJ72JI8YJ3RO/5ZHkhIDeY9Ckg98JgTsusEPU4XdCS8TDCxijr/ickFF5zQimHJ8Tsvfdug5Th88JU9Nf9mAZAN44ObkgJSZNJ/13Zl327FzrfK6CW0WxDaoz5M/O+/MKoXxHTsGCQoFCscK2ovyiQ8UikG5tI8uMlPNbkI3dV11bUL+l8QesydLbNKFqMseCpVt47lSPjsQPUicAABwRReLi5DATAAAAAElFTkSuQmCC',
 		),
 		'klarnainvoice' => array(
			'machine_name' => 'KlarnaInvoice',
 			'method_name' => 'Klarna Invoice',
 			'parameters' => array(
				'method' => 'IV',
 				'brand' => 'KLARNA_INVOICE',
 			),
 			'not_supported_features' => array(
				0 => 'AliasManager',
 				1 => 'HiddenAuthorization',
 				2 => 'Recurring',
 				3 => 'Moto',
 			),
 			'image_color' => 'data:image/png;base64,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**************************************************+0SwNKKRE4VKJGe+oh5/Xvo/jl+C7BU38ThCd1gdEEsP8AZu3oWYHbXbWLNigctRo2TlBXQ/Nl3gLdxfA0CLNWv42XenwFWWa5xAkg89gw7IuQH+PhzABirQzJ9LX/I3qFjUBCZO813gMs38WtwsYaXeUFGMJ3LHwGWVV3g0eDHRIM389LccHJfz/rgNpBPTAKF8bpHkHFsdMERnwEO3fsxRyNxjNJ8k2h3FDCmxaA0LiKb7FMa2autt7inXH4C8Ma8bM4asjxeBVFxGJUT5ZVUhJLNsJOAf7fnomjXhv5YbvgnPRpz55PRVPsKMB478h2NsSDjaUor7UrzT+ztB6v/5sGr05Zx6uiONVGaH7OyWlvpZ6q5VtvzA9jRxKUn2PNQnrPKzSRf/SB6ik8AMzmDSLR+vGtJvx/mwQrTD51nJt0sdMzYfmHZkKRGe8GiQ990FW9Hzu+0BCk4tZSYDwth9i6tPMXaz1jRl0YWhrYDXF7dfrfK3um9Kmu2Ez1pyRvAVP/DrYY6SqXsXa1GWiNXd6BJCweWYy4A69iF7PAe8ik8ndYpwHLDHWe+yfxf/N0zwPHnT3FkRX4F2c6nV6F73yLA3/W4oXGc0oIXAJrpZ6f1o89MjzJM/sI6d5DScIvs1B/YXnUPpJX3CWDHPJYenW5Cvh4IMWcWE+3TAFOjA1nldWAMKFBCh7Pbo5RhhbG9MzUkx63inmeKC98k8x8mi0ssAfHJ8ZcIcFiLvsxWeOSGJjKXBJb+aSyIS25Si+GgiQsrLt0NITmBHVKSI07zYqcH/VlbPNfPi4aBuLjFiW+libVQWJRx12RVmbzzhWdyU6DwrFlkvU+D5hyrVPEXWXmTrrA1aJnBAPKqUHhXOJ7ecnFdP2nF3k7u5Or70duDyLCQdMfT25sarg3NbHTBcIgqGUOrTW0A5wyBmMJxICsf297zx9Lv+Rd3wGDy/m+/Ns7+MPVq2IL9VyKC9l38wwzVmcl04R1lUZFupBNd/OxKE6tfTvPa38NrsR4boY+yuPKNsmm1AR43Bt98eFuF99iT8BFlGhO0uylo/r4WwYf7r6wPSr22EFSmMR1k6ePT+pH2fw1e+p3G2RepAAAAAElFTkSuQmCC',
 			'image_grey' => 'data:image/png;base64,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',
 		),
 		'maestro' => array(
			'machine_name' => 'Maestro',
 			'method_name' => 'Maestro',
 			'parameters' => array(
				'method' => 'DC',
 				'brand' => 'MAESTRO',
 			),
 			'not_supported_features' => array(
				0 => 'ServerAuthorization',
 			),
 			'credit_card_information' => array(
				'issuer_identification_number_prefixes' => array(
					0 => '5018',
 					1 => '5020',
 					2 => '5038',
 					3 => '6304',
 					4 => '6759',
 					5 => '6761',
 					6 => '6762',
 					7 => '6763',
 					8 => '6764',
 					9 => '6765',
 					10 => '6766',
 					11 => '564182',
 					12 => '633110',
 					13 => '6333',
 				),
 				'lengths' => array(
					0 => '12',
 					1 => '13',
 					2 => '14',
 					3 => '15',
 					4 => '16',
 					5 => '17',
 					6 => '18',
 					7 => '19',
 				),
 				'validators' => array(
					0 => 'LuhnAlgorithm',
 				),
 				'name' => 'Maestro',
 				'cvv_length' => '3',
 				'cvv_required' => 'false',
 				'issuer_number_length' => '2',
 				'issuer_number_required' => 'false',
 			),
 			'image_color' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD0AAAAyCAYAAADvNNM8AAAE3klEQVR42u2ZXWgcVRTHF3ZXAgpWqHTZnexcZmbXaMXGqqiYh60BpSBGiVih6IIrRght/SCNNsIW/KgQS8BYtERZdjc0iA8R+6BYytaPJkUKQQLmIcIWs84M7YMPQfrQh/H8JzN1a9JkMnOSLXQOHObuzJ2753fOvXfuPTcSCSWUUEIJ5SYVPZntMJNawUhqRT2pjRmpTMmQMsOGpA3Ssy4rkot5aqhU3xItmz2xqlGMlo3RaMUo4Rqr6EN0f0/khL61paAXhUgQ0BECnCe11tBFM5WtNlKZzmUN1ayYDVoxaqRXSK019Od4xejHe5sHe+f22xBRgHiAXaampE02UpqEtiiiOYrmvAfQZYr37OhvtBiJDkGGz/qBbdY/09v/eeLw1KQf2BXgSzQs2jZq3HaRwZeCAjfa77GOPf+J9daBmvXQx79bHOCxsjEdKV1M8HZpSdXMVObvoMB/td9lHX/uqA3s6v1H53jAaU5gG+f2GE5l5oICQ795fP81wNA3Xj9jJT+vs4BHy/oYCzRFeIQDePbunDWw7/QyaOjLg2etW8o6T8RpcgzcrcngyxzQ7ji+nj7ANb6r5kzAycteaLBEeTVg6KsDv7BFG999X8BYRXHM1tCJ3YfWhIYqx/5ggY5Xzaq/b7Kk5TiAoe8XvvIEvevD37hm8kVfM7meygxxAF8QOzwBQ/cOneOCtuLj5iPrjzQ2DQzQ53fs9gy9/80f2aB9jWsjmf2OA3pq57OeoaFskS7rfX4iXeOAPv3oiy2BxnbUD/QEB/SvnU+1BJr24fmWrsS8Ar9y8CxfpP2szGj27uP6ZB3c94Mn6GeK59mgI+MNad3Q2OhzQY++8KknaLYdV5ClKBk8zQF96rH8msDYbd3xxQJX1y4GgM7mOaCROHj3tZOrQvccZuval3117eb1N01oMxzg3+7qXzXK245f4MqiDDPsp7PdHNDImnyUr6wI3c235jZuPXRimxBiS/CE4FL2k+Xz9U7/99cAv/T2FNeW8gotSLpgrybEkzyZUKbFyrmdT1/NoCBjcvuXC61bjHgc3yNcq7TCwBku4MVoVe/d0Nw3jm8CZ0YlbfThD366D6nbgMBz8VKjc1NOOeo0UThRX2/+rIb8eXNb6JY+TjmMeMUsbOrRTtMBwFZd0vbiqIa2osYKkHDKNDnoPbNdvXe1tmLjZjdSuIje9aJKzvkM9TbsRCPQ0Q+pe17lSyiCbSVDQNlPLkK5AQSLClWI/I1soNCoC9NCIJejzxkMdsvO8zYsEuiaIO34372rYzIjRCcUZQLeo8hiBu2gDu7jffptJ/mUdLqbyj05r4f83KKkRVGV5Uukp1RZ1DVZzNmaFiMwmO4vaLJ8kq6LalqUsjTp2fWpjPownJ73oz7dH7OdaJdFHW3bUZdFjXQasKijyHKVrsP4z5ZBQ2E8GTGLaMBwGIpouIZR9PpsUFxled6BNhyQYcdRRwDpvu/+h92WMyHCGc333d6z+ZF2xp9jhGguI8IUmV5AAVRpVx6Ec1T6dKH7OmvknCqpmtOl4bQE4AC05Mz/HIA6SlrpwjM4j2VTsV5BNGGE44BBdF+3vGSk3Ev6Nf0+QN244EQ9TyCTuAcoXFHHHdNLTpOHUAcOcNty5gOCFRMYMg68gMPC6T+UUEIJ5WaRfwEXqiK6EJFqegAAAABJRU5ErkJggg==',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD0AAAAyEAAAAACaz1CjAAAD/ElEQVR42u3YbWhSURgA4LV+BfWj/QiTRT8GfS2o1VqBxoqlkVK0IHCORqu5NUY1yMTPogWtkJWMZKUlZYxoSa2tIbFoGAuWGw1ciMEWijjjLm130j6Y7uTNPnbV63u0oKj5wuWC5/p47ns9533NQn/slbVAL9B/Be2z9RRYrIaNN/JbmzoaXOaIMHHMVK1T/4Ld4baQHe4ep6Pq8/1fpMfv3j9VHylT06OyVO9wq76PmSt06m9JNZtUbHoYKvrK5wozoqeXW6yVpfHsz7i6O3AToff2JkM8+jOaDI6qtGliTLadmY3F0aB5KTP7PSzk7NM0aJe5WgvB4sP1qxR2pR/GW4ZCOzHpD21VRSAsOK1T2KlQ1sD4LWmyrGcl5viMAYLL1CcOxuBo+JSjMP7IiEGbX8NwhUM++4O2K1eosmD8vR2gP7RVdME0leX5odwP09fbAdqwEWfOdDgaCGfeTn0KOiKEn+wydV1uAm1XbYbph+0paOchGC5Tnz2QSCslMH1BTH/OafTjyzBcrkiEo3Q7TKvY3vWM9I18mK7cl5Teh0PTs02jL6+D6WP5yehotjFo+1pG+iIfpqumMqd7nIx08wTGDTdlTr9hnjXeSpY01ytxaPqKRqOf38b5ccnHk9BbcWjyCCMduIlD148loWvSX0rjFtJzy2Ba8iqB9qkWw/QLdkr65SKYFh+WzcXNWQPD53n0251AR4SKHBivmaXPWdkJ01YOuF+/FWPMW3BGNG/OCIYbjWQ1GQILJIsVq1g4/Y1ugTdMzSZ3J0I2nLIQZ2k5dpKqVPAqFPpSkpKOCHEWl0qT/B4MXxC/HUizBegpgOrSO3tHx1uGUsPXJkbvZdD4fNaaXzNVahf5LnNs1Ju1TP1Ho3FAl6r1Adq9CXev+eruE89+PF5d55a1Gb1K+qiRukfGaxPz5/pk9UgdU9eRdpNLjBFjVJfF/Jor/FT8qTh5p/FvtvZkyBL4rbTX7+f15YYlZIg6RpuiGRsiAsOe2Nn0zNcqNs+Zh1DnR0F05PSMM48IDLIQ6p3szqauyJjWFRWMlE9xNSW2EluDbHpmh+f4lg0uKScoLhiRcriasMS8p0EmX+L1N8i4Gl0RGRJpSxt7J+VL6ocu5ZRP/RKtKwpL+MQgy+sXabuzqQ9rDUo5rcFin5SzTdE7eSmnxHZFTYao96krRFo/DyGuJnZO3Z+MaSqDIq3XHztucFlZJTYpx7GGT7zj9+Ui1Jfr5gh4gywiwNUMe8KS2BcQ8Pq5w55iX/yGkQbdnd3PRahlV1BMHRGysmoHTMIH/OgWE6huNgnDEpOwdoDKNULX2dXNRIAahdCw56T++JZ+rtfv5iz8b7ZA/6/0FxX7gz2SYuOMAAAAAElFTkSuQmCC',
 		),
 		'mastercard' => array(
			'machine_name' => 'MasterCard',
 			'method_name' => 'MasterCard',
 			'parameters' => array(
				'method' => 'CC',
 				'brand' => 'MASTER',
 			),
 			'not_supported_features' => array(
				0 => 'ServerAuthorization',
 			),
 			'credit_card_information' => array(
				'issuer_identification_number_prefixes' => array(
					0 => '2221',
 					1 => '2222',
 					2 => '2223',
 					3 => '2224',
 					4 => '2225',
 					5 => '2226',
 					6 => '2227',
 					7 => '2228',
 					8 => '2229',
 					9 => '223',
 					10 => '224',
 					11 => '225',
 					12 => '226',
 					13 => '227',
 					14 => '228',
 					15 => '229',
 					16 => '23',
 					17 => '24',
 					18 => '25',
 					19 => '26',
 					20 => '270',
 					21 => '271',
 					22 => '2720',
 					23 => '51',
 					24 => '52',
 					25 => '53',
 					26 => '54',
 					27 => '55',
 				),
 				'lengths' => array(
					0 => '16',
 				),
 				'validators' => array(
					0 => 'LuhnAlgorithm',
 				),
 				'name' => 'MasterCard',
 				'cvv_length' => '3',
 				'cvv_required' => 'true',
 			),
 			'image_color' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD0AAAAyCAYAAADvNNM8AAAElklEQVR42u2ZP0wTURzHH4h/U6QGkHp3xMYwEGVA4+BA4kUHB0wYHDTBiEljSCSmJsYwOKBxINFEBvyTWNobHBg6EMPg0MGBwYGBgYGhA4MDkVarAu3Vozx/r/0dnqXCcfeDGr1f8g1H3rvX93m/9+79fu8x5plnnnnm2X9qaSa1LzIplGLSECiSZrKWYsrTNFMG4a/KGauz0w5/0ezTY62Xcpo0lI0pozlN1op/Y/KDbEy6mo3ISlVBF1kwAKDDKSYnQXwLLYHGF5ncWaktfUzuBrAEyADxLfRB15Qw14IHdhG22VfyaBGEO9DkZ1by2LImd+Zi0rQN0Er6COrbceAFFgiCt2Ydwq4rXSdljTv1b6HTBYfA69KjyoRYFjvkYakLPLzgFjhVAx1VD3Lex7gxVM/dQqNml7VggBhYaYMOp1wDg/TzJWBTxuMGEnA9Js+Qeby0huU5CuDlkw2/ARd1k/H8SDMVeJwEGjo7QgH8pb6Fr12v2QgNWuvfw3NjEgm42NoopnWOZFqrBysCr0/zR4ep1neSP7QXF/zBy1KEysubARe9fQu8HZWppnm/I2ARRVF9vFZO128JLfTjSSOVtxMOvayoFMBCxuV9tqBXBw9RQRuOvuQQhDygAE7vkWwBF6f47b1U0Dwfk3ocJBEiaXAPnWlstg0tti8qaEfrGj5i7yigvwaa7EOD9NfHaMAhU3OyP7+vCvTLAI2nNXnEyfSOVwOaatvKaspg9SIxX8D+hyxUS7amHaWdaSYNUG1Za701tqAL4f1k0CvR1rPbhhaJPhV0/sIBW9DGEFUoKi24STZmKKC/nzhi7yM2epQKOuIGup8qQClcq921qS2OoRxDY/ydJMmlTzVsCp1/1kQDHZXHKfLpbpK1XSPz1Z69lWPu+2Qx95I+prRRnY8Nk2xf/qMbvuTiAIEqCnN9gFBhmk9SgH9TGjm/8SuH1p+3VC/stAdOc6jwTWosFEJ1P/QXLWsUaWQ2Kt/b4cN+Jez2CEmEuCt3j58RtxVu92NxDbRrVzrodWObwDOf2K9OirMskQLibcV20saMmM47dsi/VdSG4WpiE+/PwewYhbrn/rh0AF4k/QD0ZpMBWBLbUS6q9Ga0oP+vub3MsKBf3GCKo6bSTaYzTwio/FhrR05TVLEFVcWjnjkzETB07sLvDFL9jrgYC2LHzUP0jgpQ4r7YXLfiWaR2PnweBmmWcrMNs712UBO+Y75v7bwf+9COz+X1zXLxGyoFdF/xlpaxWdAcaBr/TmK5yGiSqHkEmcMOTOCgTYFmQE/xnWl8fx4HRsP3xWHexWL0y5iIocOgbtASvp9CKLP+AJZnsM0UJfQ4wkxhx8SluoGjnUSwLnyuQ8/G0SNmG2bEpGInzY734nMIy+NY37QE1vEhmFpW/x3Wt5aTQJsdtk4fjoAfQVdAI+g5c+p2ocf9WB7HjilYT0Wv+svaNQfMj3Vf4f8qelwtqz9aody1XbKMvPVD8d5SnkDocfR+BMuHsY4Pp3oCByqE5RGsb203gG1N4WAFsd4EwnaW1TcHZtJS7plnnnnm2T9nPwFAFOS9Sg2C0QAAAABJRU5ErkJggg==',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD0AAAAyEAAAAACaz1CjAAAEXElEQVR42u3Yf0wbVRwA8MbwxzCNIYElJA7SxDrvj6XpH51uy8UU0j8oNLHOklYJ9RIuSOxi6rJldckytIcFmQRwPzrTSBuLOqhIhOF+dDuRbGRrHMjEklRSpRmHlHCaQy/ZWU5eagPH9fqu1USN3PvrvX7vPu/de/d+VMH/Y5dih96h/xV0wnF1V39FT2mXy/fR4O6ZqpRGHMNFlupme++5767dc3/36ULgt8a/SK/G+g41vWwMCNPh/e3d877NqMXOcc3g0IBVmG7sjT2ZchREs/b+isP7t7Ob6TS3Uszz9PT1L7ajm2nk5g+786aXllvmpNk/W08MPzP4iDScTrfKuUge9Lepl4ZhcO2B17WE50IIRg9Yr1ayv8qkFw9aV2GwMeC8SHhAer8ajl+7nK3lCnEfN6NwuOlyGiY8bZWBV+H47XEZ9MVrcPj562+9l6EJT4fxkhKOLwQg9OLB516E06CXtyYvC6fHXltvzEn3lMppsxAmPO2PX/oSjs8TOeiURs4Ae4XaThOeD96B0+OaHPRMFRw2BlzfiOneZTg9OCQc5wL64wU4XHdODBOezho4PWB94JOku1xwut6ajW6rlEMLe1tAn3oUTtveyEYTnk8eg9OzvZL0iebC6f5zcHr6uCTdRhVOy/m85p6WpGXNZNXZ4Len5fS1cAkV0CNn5Hxcb/4uprsQOfRqvSS9UiyHPvqumL5wBw5/bso5kTrW4LS9T0x/eB5OR77PSY/Vy5lUWncV8rrp6Zx0SiPeBooTbhLS/jE4POmGrtd3G+B07YGTtzbhnjAcHrIzERkbpL5DcPyFpsw47zDKmcfEG4WsdEpzmoPjDUXun8FKHazNdwLNuSNNaeRsGRqe6KjsR+FL5Ryb5xFg2APbKrVRP8Vu7IV9y0t1BRx8VmM9paZT2VnH2tcVIGa9cZ4YuZmd/Yyf7ZXa/ss47q0Uj5w5Wb21/c3o+a/mSrfGrDc+8N15dmsFhuyT7h+rH3b+LYfctYGEY6Yq4WDt0jEPO395ajnERHK19L97tI/Fpqbyvae9XXyPiKaoeDwW4ziev38/Q7Hs5OTGkYiNRBiGZV0uDAN5EAHiotFkMhIBv6cfT9PxeDRK05lykMcwkoTSfn95+b59CKLTIYhpY5nDcfXGpVJxHIJgmNlMUSiq1R47xvM6ncmkUjEMhqnVXm84XFZms3V3j44qlVptWRlJgvKzZ0dHS0p0OpCXQdtsHIeiXm8iUVSUTKrVFDUxoVZznMtlsUSjIKK1ledJsqQEPDwYxDDfxibXYvH7wf0GQzDIMDodoEF5TY3fn87LoMGD0y9IoeC4PXtCIadTpQKvd2ICQWg6FLJYGCaRUKlIMhym6XQsqBhNJxItLRYLSSqVgAblR45k8lD6yhVQ+/Sw0OtB3mBwOm22ZBLH9XqXi+cZxmw2GDjO59PrcTyZTMdSlNOJoqFQPI7jZjOGTU2ly0FlTCaQ3/nfbIf+f9F/AOHCDwE/4NgTAAAAAElFTkSuQmCC',
 		),
 		'openinvoice' => array(
			'machine_name' => 'OpenInvoice',
 			'method_name' => 'Open Invoice',
 			'parameters' => array(
			),
 			'not_supported_features' => array(
				0 => 'AliasManager',
 				1 => 'Recurring',
 				2 => 'HiddenAuthorization',
 				3 => 'Moto',
 			),
 			'image_color' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAABYElEQVR42u2ZLW/CUBiFKycRCAQCgUA2/UgnEPUTkwjEJAKBnERMTkzwAyonEfwABGKCHzCJQCCRiAl2TlLREEJp1+62zXmTk9vb9H486blvb3MtS6FQGIsgCLqu675Be8/ztiifagdh23aLANAJWkEH6Mf3/U6tQBzHGWPiZ5Qj1glAENTnRieGSewy6kgQaMj2YRg+xPdOWfsC/GORIOec+sb6mKBc5u0D7cMqgPxZpYHQ9xSuXyj6HmUEbRKWyvX22A/6m8bJoVwQZKReykLvY/DnOPXSUl8XvueEV3hugXLGtMwsd2VNmgUpMLkIRCACEYhAGg6CXa3NgdKEdoNKg2CQD9xb36FXWeufrNVhvQgZBcHG7zPHT9dVyVoCSYDEGSnrD9Rab0QgxWWt90Z8R9CuLWtVbK91U7XZ/aZJ1hKIQAQikFQQfjt4lFCmkscTOuhpOkhkUANLoVAoGL8gCnE5V68QmgAAAABJRU5ErkJggg==',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyEAAAAABrxAsuAAABJUlEQVR42mP4TwfAMGoJDS15+XnaSc/1sYcOH6aZJZ++eq63nF9g41ZplvnmBY0s2dZrbLzzxP//b16YZc6sp9ASn2fYoQOPsfH58/////ztwGM5H5eqS2eIssQYDwgOXruuuBifijM3KbaEMCDJkp0ndp7YDAQz6xt8kpNBQYXPfw0+Kxe7VZJsydPXqKKPPh64M+1kcXGCDiTsg4MLbDqjl908fPjTV1hcUmwJYTBqyaglI9WSm5fO3ESH9+9T2ZLebWkz0eGCnKEYXG9ePH1NGFJoSaU5rsoJGY4mYaglaTNxVVRpM0dzPFGpa0IMHfLJ+/dDMbiwlV3okCalMDocTcKjlgxKS3q3zawnDUK6F4OvE0RjSxp8KIGYrbLRsZURagkAp+ib3uw6gLQAAAAASUVORK5CYII=',
 		),
 		'payolutionelv' => array(
			'machine_name' => 'PayolutionElv',
 			'method_name' => 'Payolution ELV',
 			'parameters' => array(
				'method' => 'IV',
 				'brand' => 'PAYOLUTION_ELV',
 			),
 			'not_supported_features' => array(
				0 => 'AliasManager',
 				1 => 'HiddenAuthorization',
 				2 => 'Recurring',
 			),
 			'image_color' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAAMCAYAAACp13kFAAABUElEQVR42mP4jwSK5u6H41EwPAADrghGx//+/cNrkEHSJJyYGD345ImVI8UNhNyGrCZ/4maC7sZnNz61xIYjIXW45ImOYEI5m1jP4VNPbAKgNIAHawST436qRTAhQGpOQRYnN6eSmxhoEcHUkCcloRObAAhGMLGA1hGMrpaSwBmuEexTsRB3BBMTscTUp6TWlaTWxeQmpOEcwdj0okTw07efwZG6/vhtDMPzJm4iucFETv1HbICMRjBxmQRrEU1qoFLagm1euA+OadXiHikRjKukY6BGd4fYACOntUtqoB2/+ojqEUxsw22wRDAyZiC3m0NpBJMrR0oxTko3gxpdL2pFMKnux2cGAzVyL7Uj+OuPXyQHGrnVBDUSArUi+Or9lxRHsEvhXNwRTOpoyWAFm49eB7v309cfVDPzyKUHYDPvPXtLFz9ENi7/71O+kGJzAF38YiroCxtTAAAAAElFTkSuQmCC',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAAMEAAAAADcLPqaAAABI0lEQVR42mP4DwZTZoLg/xEAGJA9DIP//qEqiriHDDFlUPnYeNhNQDcNItLtgG42qm5UUWxuRBdD5mP1MHpsY7MOVRxbABByMDU9jNt8ojyMngxwxQqEjS9W8QUD8R4mzMce6NgCAMPD2NI9qR6GieJ3Dj09nP8FxcOYnsXMqbjyJa58jC+Q6OthhCzUw2/Mpsw8dBemvOsarqIJdw7E5pDB5mEYzYDdY8SU0iD27I8gSErZPRAeRk51DISrHmwOw13y4nLaZTliPYyteKOGhyGQAV+1Q8jD+HjYEzn2aoNwBUaMh3GZj6qCgXD8EuvhH764nIYvUxAOBGI8fI+FkIczfqB4GFfbhJrgUG7Eva/pxKi8YBlx76kv6TZU3syPJaQGAEeuaosDmoQIAAAAAElFTkSuQmCC',
 		),
 		'payolutionins' => array(
			'machine_name' => 'PayolutionIns',
 			'method_name' => 'Payolution Installment',
 			'parameters' => array(
				'method' => 'IV',
 				'brand' => 'PAYOLUTION_INS',
 			),
 			'not_supported_features' => array(
				0 => 'AliasManager',
 				1 => 'HiddenAuthorization',
 				2 => 'Recurring',
 			),
 			'image_color' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAAMCAYAAACp13kFAAABUElEQVR42mP4jwSK5u6H41EwPAADrghGx//+/cNrkEHSJJyYGD345ImVI8UNhNyGrCZ/4maC7sZnNz61xIYjIXW45ImOYEI5m1jP4VNPbAKgNIAHawST436qRTAhQGpOQRYnN6eSmxhoEcHUkCcloRObAAhGMLGA1hGMrpaSwBmuEexTsRB3BBMTscTUp6TWlaTWxeQmpOEcwdj0okTw07efwZG6/vhtDMPzJm4iucFETv1HbICMRjBxmQRrEU1qoFLagm1euA+OadXiHikRjKukY6BGd4fYACOntUtqoB2/+ojqEUxsw22wRDAyZiC3m0NpBJMrR0oxTko3gxpdL2pFMKnux2cGAzVyL7Uj+OuPXyQHGrnVBDUSArUi+Or9lxRHsEvhXNwRTOpoyWAFm49eB7v309cfVDPzyKUHYDPvPXtLFz9ENi7/71O+kGJzAF38YiroCxtTAAAAAElFTkSuQmCC',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAAMEAAAAADcLPqaAAABI0lEQVR42mP4DwZTZoLg/xEAGJA9DIP//qEqiriHDDFlUPnYeNhNQDcNItLtgG42qm5UUWxuRBdD5mP1MHpsY7MOVRxbABByMDU9jNt8ojyMngxwxQqEjS9W8QUD8R4mzMce6NgCAMPD2NI9qR6GieJ3Dj09nP8FxcOYnsXMqbjyJa58jC+Q6OthhCzUw2/Mpsw8dBemvOsarqIJdw7E5pDB5mEYzYDdY8SU0iD27I8gSErZPRAeRk51DISrHmwOw13y4nLaZTliPYyteKOGhyGQAV+1Q8jD+HjYEzn2aoNwBUaMh3GZj6qCgXD8EuvhH764nIYvUxAOBGI8fI+FkIczfqB4GFfbhJrgUG7Eva/pxKi8YBlx76kv6TZU3syPJaQGAEeuaosDmoQIAAAAAElFTkSuQmCC',
 		),
 		'paybox' => array(
			'machine_name' => 'Paybox',
 			'method_name' => 'Paybox Mobile Phone Payment',
 			'parameters' => array(
				'method' => '',
 				'brand' => 'PAYBOX',
 			),
 			'not_supported_features' => array(
				0 => 'AliasManager',
 				1 => 'Recurring',
 				2 => 'HiddenAuthorization',
 			),
 			'image_color' => 'data:image/png;base64,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',
 			'image_grey' => 'data:image/png;base64,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',
 		),
 		'paypal' => array(
			'machine_name' => 'PayPal',
 			'method_name' => 'PayPal',
 			'parameters' => array(
				'method' => 'VA',
 				'brand' => 'PAYPAL',
 			),
 			'not_supported_features' => array(
				0 => 'AliasManager',
 				1 => 'Recurring',
 				2 => 'HiddenAuthorization',
 			),
 			'image_color' => 'data:image/png;base64,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',
 			'image_grey' => 'data:image/png;base64,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',
 		),
 		'paysafecard' => array(
			'machine_name' => 'Paysafecard',
 			'method_name' => 'paysafecard',
 			'parameters' => array(
				'method' => 'VA',
 				'brand' => 'PAYSAFECARD',
 			),
 			'not_supported_features' => array(
				0 => 'AliasManager',
 				1 => 'HiddenAuthorization',
 				2 => 'Recurring',
 			),
 			'image_color' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAAVCAYAAACNDipWAAAEpklEQVR42u1ZIVAbQRRdgUBUVFQwSTpFVFRUIBDtTAUzSAQCUVGBQCAqEBUIBJ1BpMl1JgIRgYioqKhAIBCICGZKNnQmAoFApEM6g4hARCAQ6f9/b+/+3e7tXdK004TszB+Y7L/d2//+//v+PyFioy3mH/8S+WUU/F9Mx2QMAHO2IwpeR+TvQfqhFA4B7MLUQmM8+kLMAJh1H9Se//9RR+Ru/N+6DwrkUqMgyo33wpO7wjtbGsszfKzPkuCAKN3W0QqRPBeN6lwR565F/uRBgOvJFVGW/UAQ5LE8Bzimdk4A8BLkAgG16cLcGYLMwZ/YUW7WJxHgu2uRe5ukey2eLoLOrUtncgCWbR/YK0rVY5uJGMDImCEFV+BvLUH2fopnr9tibt6S62fAGJtwZ9UiUmpWwECvAr1PjTeGTqj7QVRaIWMvyW011/Ri0eUpfZjX4/OPJ7D/hnVd/J0O21i17y0P6N3xDDaAaQ14Njwnpu+q85xK9xHM7YF8sb4P7WN9nypJ8ftzw854znJji96Z277UXKZ3MNc6JoB9gtWNMme7QASvmoQEF2cpLSr39GIKtHWHXp+MYabJtjW6cD4wpOw41qz59+quc29P7ph7xFJ0qfnV8XwvIDRqjSMyLnecrAPthXvxUTybh7OcwLkXYnzhWyIJ1BEMxGopC7hKcgeJKS1JSs132QCWdwTYIACX5GKK02QDuNy4TAW4LG/dTtJYjRj+z4jeRgRMZPTlxpqF6e+lpmhIv+vZAc7XLQD3w5TWnAvTcQJJQa9GjySBVFSWh4Eu/jYIwHiAEKRiptJB7+2dv4TnWsHzxh6+c8TPiZGkSxCMfNs5ByFmaAMNhhYMCgwIvp62DQeQ6/x1gJMMwg9Ld6jsJkbBoABzR8IMQM+BYIrDOQ4sXgGYSpP2zgwwrO/JfeMcgwJMzkn38g4BxQWddewAjoIxGoAV8blKTpsQpcpAm6l7ZwbYkDMyNL+DMwEsTyPPuMAbC4D5HUzMU3urvBgaYDoIplpijW1f2F3ps3B+ByNjx33pnpM3QwLcpTNoBzLBSwc4TqT+e4CVRyrWiCWPG+DlaEmUCPBtwMJx7SAtNuvuNqODZGkip979cKgUnRR5gwAcX3/UAKONBwfYyqK5Z1/44ISli65ZkQXyCFB32am1pMIyI0re6tFU7AOMTFPfu4HwvSHK4k5EAJLeOfvtJgPA3Ygzx/flxnaBF+oUHXNr6QBT5tpyONk+VRnYvDC/INkFGyIpAJt1sGbWinX2HLrH0bLAta6NRVv1Fvza8kVKKVdJBRhLH3ctzcgkErqE9B3qbBkNEl3b414cUBvAlNFgH94g4swcg419bFgD8E5B2jYBcFv40QGbIo4U3WP3YJvSX7wIpyim6An1qCUIBuYHUORp1y9j+Jr30ToYDBSd19IiJ4kbFAlRRA/qX2SxOnNo8qOi3LN0kmo+Z7DsyaKJdP2uW1InSzVpqqqBwjprGNnokKYzmG1T1QCJ77GfqD9k79ZOskb/+SvbHTwdI2/Ojx5g1RaMRwnvJFWnhh9ngOOf7Mz7bmVq+H/6/ZRY5PYII7hismNLh2o6Usdvqzly4PW2s40AAAAASUVORK5CYII=',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAAVEAAAAAD49anJAAAEQ0lEQVR42t2Y32sTWRTHW+j0P/Bpkbzpk3nbJeShLEgI3cUKWyRLRhYswpaC2IqNMoHBdAujGKaCSEw3DTsEQg1MAxoToRRCl27woa21oCWIFdyWUIO6EuzYMlx7enq99zZTrWCq23ugJGfOzbmf++N77rSJbLXayVltVqudJPu8NcGftSN/Bn5y+b1gEd+LiX0ObLv7437v8Rf9cfXnX//we08Uvg7yq6PFjlyxLDUuw3rLessG8K2HsK4vr+JaJw/6vRf9e487P9TdCZYrNi5HWSpLG8Cnv//9u7UjzH122u9F/L1surJnwMek4gPBfeyXOdGzFy0sd3eqba+ONjLHFvCsdvOH6DtmhvfRzcoIDbIjk5YRRMuYTy+D70kz9YCNn3p7F7z3XhtBswt7mV1G8N7rTfX3TAVY7FSAkLnD7HtqetKyIwzYCM4dhpzzQ2m3mJMQS7stJS32O4Sw30m70+6V9g8VxzNRTU3j6BekjEmjrh/aALbdJwqoz7z98xftnDFxq6H15GoeQko+3tfdmbTopgzLbL10BQZ5cY2PNIKE5Ipi74KL9qBbOiHzz3tb11sg4oZWlnBynFrNk9jKXc1fyz6/gJ+Hx3gR3FzhuXQ9rt87NMJvNd7u36kHPnPA0pyBn70RI52AL0nbgfseixGw6jD4j2/YqQBiFjtmYlT3b0t1W3rc5wTcH6dBkFBt+28cNjIvKnakmq/mV9pjFfBW887AZQmeZlP1BQJ6L60OLsJz2gOmg+a8loUiUnCxnM5yttIOGGD375R8GAejQUD0fDYwPxBMa3ad9/BrsBMwTtKZA7qiKwn5STPCJq3eVr63E7CujC7THDsBlyUjWHCVfGjZVMOAEWM3wHZEbePjllYJmbS293YCpnbFKPnwDNcDR0P4RMRrADCe4YwJ8zqgfgyYkKVVIxiWwzKeS1BxPMPjp0q+qUAovjPweU/GhAmirR6YytQXB46GQB3f3hWBFyQsRjxw32NQcTsCmxGB2cVRFC2QOUJQAZy3NL96zsA0frfACzsDM5XG2R5QdQULDFTXmRiugK5EQ6xY3dBQ4HQFtzEAP78ApxcMe2dMOkVhWVe0H+FTKL4dGM9uNET74rBFPGiiGM7EtgMvrU5U+YjR5Wdvmioj9D2Jt1lNBGZ1GPR6vUUUneuHsByIsUylmUHhqPSJPpgEEXh4TIzAtU1a/AaHNlGllxKo+MNjCMqA7UjSwksR6vmAuvny8Pe/517+ZjLraU4etN08cG8rnMKwHKvQMj4T0xX0qW0ZExPYkVxxcBG9PTkEfnoZv4MNLhY76ECvGOi7JBVcsDtAgMIyvafVPEZwQGU9cZ1qHri/8TctS0u7EzLe2LKpSh/9dXZBreZpj9Fl9Dd96v7Ji9Zum9MZ/qb+AfBlgBMfVgQVOe3e58D0BY/a/ND/EhhUEt97PtUyJlVVdqv69tp7fUWmd6agnD8AAAAASUVORK5CYII=',
 		),
 		'prepayment' => array(
			'machine_name' => 'Prepayment',
 			'method_name' => 'Prepayment',
 			'parameters' => array(
				'method' => 'PP',
 				'brand' => 'PREPAYMENT',
 			),
 			'not_supported_features' => array(
				0 => 'AliasManager',
 				1 => 'Recurring',
 				2 => 'HiddenAuthorization',
 			),
 			'image_color' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAABYElEQVR42u2ZLW/CUBiFKycRCAQCgUA2/UgnEPUTkwjEJAKBnERMTkzwAyonEfwABGKCHzCJQCCRiAl2TlLREEJp1+62zXmTk9vb9H486blvb3MtS6FQGIsgCLqu675Be8/ztiifagdh23aLANAJWkEH6Mf3/U6tQBzHGWPiZ5Qj1glAENTnRieGSewy6kgQaMj2YRg+xPdOWfsC/GORIOec+sb6mKBc5u0D7cMqgPxZpYHQ9xSuXyj6HmUEbRKWyvX22A/6m8bJoVwQZKReykLvY/DnOPXSUl8XvueEV3hugXLGtMwsd2VNmgUpMLkIRCACEYhAGg6CXa3NgdKEdoNKg2CQD9xb36FXWeufrNVhvQgZBcHG7zPHT9dVyVoCSYDEGSnrD9Rab0QgxWWt90Z8R9CuLWtVbK91U7XZ/aZJ1hKIQAQikFQQfjt4lFCmkscTOuhpOkhkUANLoVAoGL8gCnE5V68QmgAAAABJRU5ErkJggg==',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyEAAAAABrxAsuAAABJUlEQVR42mP4TwfAMGoJDS15+XnaSc/1sYcOH6aZJZ++eq63nF9g41ZplvnmBY0s2dZrbLzzxP//b16YZc6sp9ASn2fYoQOPsfH58/////ztwGM5H5eqS2eIssQYDwgOXruuuBifijM3KbaEMCDJkp0ndp7YDAQz6xt8kpNBQYXPfw0+Kxe7VZJsydPXqKKPPh64M+1kcXGCDiTsg4MLbDqjl908fPjTV1hcUmwJYTBqyaglI9WSm5fO3ESH9+9T2ZLebWkz0eGCnKEYXG9ePH1NGFJoSaU5rsoJGY4mYaglaTNxVVRpM0dzPFGpa0IMHfLJ+/dDMbiwlV3okCalMDocTcKjlgxKS3q3zawnDUK6F4OvE0RjSxp8KIGYrbLRsZURagkAp+ib3uw6gLQAAAAASUVORK5CYII=',
 		),
 		'skrill' => array(
			'machine_name' => 'Skrill',
 			'method_name' => 'Skrill (Moneybookers)',
 			'parameters' => array(
				'method' => 'VA',
 				'brand' => 'MONEYBOOKERS',
 			),
 			'not_supported_features' => array(
				0 => 'AliasManager',
 				1 => 'Recurring',
 				2 => 'HiddenAuthorization',
 			),
 			'image_color' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAArCAYAAACzfkyLAAAFiUlEQVR42u2cPYzURhTHjyIFBYpIQtiZ3UBQSEgipYgQUihOoqCgoIgQSERKsWuP9y6nS4QihC6I03q9K0IkOKEoBUgUKShSpKCgoEhBQUFxBQXFFilSUFBcQUFBcRKZvz+E8XrteePx3n7Y0kh3q7Pn4+f35s3/vb2FheqqrupSu14vvN7lftysue+1vuzssw9f29t+t1qVGbk8vvx1l7Wedpm9HW8d1rq10djYPTUT+fXA2t4+X/noKvth0eXOSTT8jLbeWP3q8sELzHSfWKCr9aXjk7ommLOEuZWEGzWXNe/qrjXWlHrf7aO338ELpzx4j7cFBtndL56NmsRwaz31mP13n4s1wEenFJiyz1WXiz+73HkSPXNSAfvjzFkPrEEeEI+J7+WzrnvMedBl1nPc53Hrn6y+YWxu3TmNde5x+6+4F8kcdO/g6qEOb96Rf/hKHWpG4/YLDMDl7W/j/fxS+3Ffn7UvJGGmtUkFLMf2X978ASBpnT6QnDmnAcZ6dbn1CGuqtV59bl80BnYY9L/xvvBmq947wYBz1wp7cdLyVOacBhifaa0XokC41VLAzjRgsZkLii0t7zjg0CVvV4BpV+jxMrenZPA5dsAIpEqHO6OAfc+Xsehe3WmmBUdjA4wojhYhV4BTRQ4u3Cj6DZrYHBU9jxUw9gc9YPIoJDsLOrQeq0STswo4eTLIEzfGC1jxBr9JS/e4c37UBOANcDiHW/L3dAl03gCr7dtjBEw4Em3h/KYzGf/shkO4YcAIXiIlTaVRZENoARAR8LL2mX3F5dYfLneuQYSJu2Z/fjkNVr2TgJUWGEeoom9uj618YRKwx1v3KGJLnqIG6dUXILJikthLqgtqIgFjnzXtqooAhoVRBBlsGSPBsqVTacmCOQNsb+MNnxTAcJmUsa/XVo6lRb8dZt/UPQnMHGA0uDBE3m5DnOg1fvpUNZFgEnBgveKl+rjFZlr/WuLOFAIeFD7f+tG1fR/BFCXFpQ3Yz5yojw+ZmlTRvuBZfioAu8zumxc0nCeIPvOsWwcw5Z6wDeCK4/3+duTSnqwc7kwBpgYr1MXIyoVSAeOYQ/U4qdbLxZoJNW4qAIdJa7dMiRJewgRg8p4pPUnSev1tCflUvbkMkLueOsC+Hs3Ew1Ihc3G9COBOfeksvc/hqD90z68oARqqTNK2m6kB/Mb9lQs5mVkh7ac5FQxp0X5RrwFrTfMAUwk4suRgf6IcQSjNeh6XOzUCJtW2NaoAMBA1lCz3Jay9DE15xwC/VczlCwnx9JeZFq9PKgsw3PnIElcZdCkCflhW0mDHAcctGoJ7ENz4KcGt4gDeiA5lAM6SJCmA854zE4BH1uvWVo7BSpBd0SkWiLIrZQCGwmYCME4Wcwk4DbhK4dnbAw8Ks8tx0dmuVRUwUoQVYM2arsjKytqDsa1UgE1W98sFpQCI1C0aYErAJzZHHW/mDjAyQnnHgbyLqjBhglTAOEMXlSjnEjAmgjMfskFwtQCuChbiCDmfKi1RV6ok7vWDNCueU8DDQgEehLMwCruxKGHUfBI/4ywbfJ+Ipi4lv8ZBBYyCv6JWXAEuucXzxVTAsEil8tyMRH8FuMSWFA908sHU8UKarACPB/AgWXarAzj8wjXBTTsPKsBlw90vnqUFb7olO9SsV7xctwJsPoP0GFUjJmuyqPVUkFQrwObLdV5Az83Kp2oX3UlvQD2aReOYO8CoVvD/B4eJ6sqwZgkWpiKeFKmLHvrek6J6NtdS5cbxc7t7H7Y/6TWaJyT0M526s+zhH3zUrHWPWTe6NfF71PB7+PnPXs1pduriG/ez7z4gSZzy79GPShtSz9Cf4r1oV7h1JKg/ax9Quuf99ue5Qo9cL5VnyZdq0cR94Qu6qLNe1TWD1/8UeT+pnUgUFQAAAABJRU5ErkJggg==',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAArEAAAAADGhc8UAAAEj0lEQVR42uWaz0sbQRTHe8pe9OTFg+SUQ/6AonhoL0sKiiKhkvoDguCPrAdBSBEsorBV2AQkWFGDWhQDC5YUf4A0kiIogQVhiQQMBcHSkhpqTK0oghCaZlmXTWZn9kd+bYL7bpmZnffJzLz5vpc8Sz+x51mV+En8ef39a2zh7uOTAD6j+qI4xtvszMNaSYBv9y/JCMOaWFOEiTDnVNKl9pUPa6d3xcRNujowPMumV9B9b/fPKVRbKnxGQYCTrr3J6ZXOOhyTWl90qpkORZhUGI65RVGWgR2uZzGBKQvoR4QBQYLmpRdjDusVjjm9uaMvScZGh0iW3yMAcDzhZl9huKK1LZBs6Dk/5m+93yNgilZM4C4DOD8dEtaTZMGZRWC/Z6S/bQH0Kwt4c1sNrGC9j1s8wsBaiwks9Wp2Rlg96cwisNML80sAJqaacQyvSGCHEXz7rrVgYDerDbecwJvb4IESAmjewHuTWnHLCZwmcl0P0GJAygs4FYbH5IoBziCvN3ERGMccxuwInSfwrhWN1Rd1ep3e4WtppCwr8ON9AEqOPIFhDTjWWXcwmD1BKnxOBWg32+vSC1j65AkMu4w6sNt99DR+T19ULXDSxSk1qcEFYjzB2AK0zzlnXG7d4jUTcUmC9re+IGCYw1PNSt/uj3fqgCfG4dIFVGusiWRzYwn/lcpBFRF4+FrttpIHjifgYsbNZr/juFZMDnQCxjHWVAzgOSP87d9qxAg8/1nuHigbMI6R7K71ZCgGTRXUAccTLSZYq8MojkdLnpIB2w1Kd25n3Tjt98ATLzlgEgETNIsCX+mmLwHw6plasTGwE6DB9UYDw1twzG5IE/zY+4sOTAdgVGBBuZGbjaKAH9ZQO0dcXzqE6wGcTq83aZOVq2fKwKizObAjrG86PdKPnsNuoCwlA06FRxu0IS+9kAc+7EaNFKP//QV8ZzmMp3fiwSkJMLcBtSILOQscGKw0iJFfaW9k1pVQKx8LKQBkVpkOwS8RuFmvePGJCk04VLBmlwKPa6U9Wkz3F+r1ckHA/AvmjHwapsb4ypIW4MPu7NmCZmmP0QYtCULBwPxKMzY3O3zdoeg+Lx/UA+fKSTgw2KcMwNk13m81h93LrejyAJe1qAc+GVIGXm/SEVgEdyAUMVfYVg8MblcYsM9ZAcDomhe3ZlrOMGOrEmDGBgfgNBccGB76MqeeqADgGANeBeCDUk2XJAo4QCvJSh2Bfc4W0zi9Nxlj4D+NofJV65WctISfezFx0BVYFAZO75xxcztoPuxmTUEzHaIsKM0k/OCBAj4YVFrjCgDWZnx+jEwPiS6DfPJfZcCCREDnw6j3HtdWIbDdIJRx0cBJF3zsmKPqgDvrxAAnV+JBZWB8ibdqgIev4wl1NS1UxWq5tWqA2xYyapdQW8SLMcgLjdAR+PRuekW5csnVmPweqUSRr0v3utAaTWdp+fDvl+/k/VH77ht6YqNx0f7hJWeL9o3GT94vqaj5ZgKuwm4mjtqlJrRGzbDWo/afGbd+z0s///FWInz+SXtFepTbMkvRA/OrWv6YVrTnP/pERkfnwcI0AAAAAElFTkSuQmCC',
 		),
 		'sofortueberweisung' => array(
			'machine_name' => 'Sofortueberweisung',
 			'method_name' => 'Sofortüberweisung',
 			'parameters' => array(
				'method' => 'OT',
 				'brand' => 'SOFORTUEBERWEISUNG',
 			),
 			'not_supported_features' => array(
				0 => 'AliasManager',
 				1 => 'Recurring',
 				2 => 'HiddenAuthorization',
 			),
 			'image_color' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAAAyCAYAAAAUYybjAAAE/UlEQVR42u1aiU7UUBTl240xhhijxqiRGDW4BIWA4i4ucYssbkQRN8QNVEAEHdBpX6/3nL7XeZ0FlAAdp20ymen0dUgP95537rm3rbzliBSvv3u1FSAUYBVglbd1Srn9RL7BCnadkujjV4kWSmJG30h569GaNeHpGyK/A8Fh7j+ruyYXYAGo8PpDCfZ0S/T6o5hbT9Jg7uslUAAyOHxBJAglPHcvn2ABCJdeDhj/uhl6HkeTO9fP+C6fYOlR3nGycv5jSYKDZyuRN7coQeflNFj3nuY0DZWrfHDM+DsJLw3Gkba3J440j6Oi91/IYfkECw/fda1C5gqUeToRf74yLGZssiZtAWIuwQIwLpIYTYfOMxUJ5MSUhP13K9cO9Iks/86vdDB3RlMEjpRj9Ozv485X3tlVibreWwQwt2BBBoCnUqk5OS1mcEyiqdk0sBmRe/OI0uNXakAJbz6SaOZ7jebKityzBWv7McqB8MxtCY5cFCn9qgFQjCF/NQO5ZwoWUin6Ms+dLppdiLWWApisaT8u0dvpNIAZknumYFE7WeIOB0Y0iiIJOs6uzG0Zknu2YGmEoIBOuOjbopjh56z9QPYkeCXzsOdmsi5Lcs8MLHBV9HmO4CTfaREt5VAkNJQS4DK8g9AhH6L5HyI/l8Q8eMG1uQALuxzIHGCRwP0yZ/S1RJ9m6vpYjLihMTEv3vN+pGTLg8WiuKM/KWVA8jT1cH7hfo2EaMRdVPj2vtYFa3qOssApdaRZot53d5PLsAsycjzlXrMr6oH31k5DLZgRXQ6IxCX9Ok+g4FOR0zyuQu0YnByo1Vs2Qlua4Kmtfi5L2e2GGmHhVZUPpeWKFaPvWEd+050Ru2IKLJUajSKv9aSDPiyiJomwo5fi1HIyQcscRFc9QOq5qS0NFuq+6M0npqR5/IpiE/57itsapBksG39t65c7EJgKErgI6QYVn1jLWvasVNYwLfPUsKBdrAQO/dSo0G4kaAnkJsuGzC0a8hJ4y2tWrNpfXChR3efSz0IvkEJVCX41yQEhmrvasJ5TCiBcsxUEHp65E9eHym3cDBTQavmQ31kH5SCodjMyzrqRLun4O0YSFX8G7fpiiqYAqwArJ2C1n4j101pmqTa53ssULG7xdo4Kh99pXvE+XUeXYXL63/8mOkSNxG0zgwUzjwNn2NVgr2A0aLWdTEWpK6xhAK5lCqe6Sdv8YGna4UjczmqQFBQOeiiAvmaizkIU+s1TSAl0qtG0QB1oyxvY0LCm+W6HSWK0og2XGOsfWRNTMWCIEqSge0jYKqVf4h/Ryw8VXx7Pq4U1p2cUdBehyVrYz1ovIuViLyeMe40OLHusVgk0F8EjItAH1GKXDwlXFA+vwDB6rF0MUFw0ObAQHRChqBl5ita93osujztPwFLF7/iQ1zCJg7bZBnar2zZSkaPucwChAIYqT40VOQAsWM52YXTCCXXOAywbG4kOLNzzf3OWHR/Cf5wOAXx0pEZHP3c5fsbsAixj5S0+NOpAC5brByK63DWf0/B9ApbnPLCnCLsHKQjO0le1Z990YLmRRpdSPtkzkizPuDVwQ+m/V4EFoBN+s+nMOVP9vh5YSD/3mwAs+b115q91jywAxk4yRrTR4vJEJkgeEcV0gjNq0wy7Gsjd97UADIdH8DuwZew1NlvhsFbrKkv++Bv8vAYJUpQ7BVgFWAVYBVg5ff0B2hNzLhhqp6wAAAAASUVORK5CYII=',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAAAyEAAAAABhmKV8AAAEQElEQVR42u2Y7U4aWRjH4QZWPuDSe2iELlewJBD5wHIDhoRtuAd3xWxvwbhCA1fAa2YiqOsLq0ZSLNJQswYISIYyjCyEtiyvHWB46tOTCWhioh3rbFJnPpw5M0P45Zz//J8Xhb/3fzwVj1jyY4UClE1mrIjv/fOetSQEDeKdV6aREiCvndyRAev98390G6rqMKMj8634SFkSYoxgSdllxBopccMQhswL7rwWx7y24JYRC4D24djndhZxbHsPVglWjpURq2clOJVhWu3vbS6MlERTDc0rk4xYDU28iGNaXTb7e2+XWVrc3M0FGbHKZlwlf29vrc/5ezXHSQhn2zMDj6wGkXUSiQcNI+VfvwmWdRXOjvmaQ1aslL0yJFd1Kl/+kCTXUgR/L1iHRRHlbKmlF91LiuAlYoW7B6tJ499/8lYRcPxyb0264CVi5dhmlaVbFwDhLs6pn+tPyBNpgpeINVKivE8T4+juD1efSBO8RKyBJ/LF3zs/nc/FmMrwUvLaxArekyZ4SVgHq83Z8zm82lCNNsaKrDNpzDobGsHS4T4tF10bKhmwzpZ4a3N2/JIEnlLpw4+TXCvGFH7nKN56zD84Vtu7+xRDTbMaCvh7b16LJjGtrz6Hzx4U6+OLwyI6e0ODHh+dG3jqT4759amN254B2J55YKx4se1FCMxN//tj4Cm4D1aJrsrmI5P4peKKPrDkWfrTL5EFXLHTZ/wzTGaCBpbmrXUqsULeGEfXVTIYxDja4fCP9+cB0BYyuoZmAjLJVx8Yq6X/l257GVfNUR0SvU1v2kmoOpQFK69lXEcmlj5NYNoc7l4NOHVKphJjc0GwxJjp0D1ttgPP19qD5MQmo+twpMC4Xjv2rEmjjPlWSWh79+evm0efky0mTrLTPocl7Eko+WvSmNe29G2vaBGy9iBCgWP+/F1ztqWvDHPsYfHri/zHRtL3iUXZYszNXat1lSxY8SL2rABIBX31TKsFS5266ZdJ47Tl3jNWswpw/q7DFdzXvzPahyH7zeubOzqV4TfComwAmH+KSLTv7XLBTdwpZQcgpWookLLntSk7Bp2dxbOlnUVsmgCMo3c1jVuvVs0B0OHSavzLrThvhS/HBY1ZPQDjKpspG64oHs1quJs0AggWAMTC43okuCesUOA0MfAAtL2U7YIGwAS5bMZ1QqxxNMdmdAAZHWXLOnFErD6HSgQomxMrd6uxFXdz85KAQD1rS09aRwiAWJjC1BzjKOYQ4S6uImKdLX1jbWG7KK2O+BoagN2ndQpgby1oKLgBTkKIhTVhjsUZ0VqORSySQ3Q4wbI/HzQEDUeme8bC1iNuFRH+3hqqBu98fBE0iFgRHyoON7rPRXwTrMQKvrk/j+/dVmGK26d8WWd1mNcS29yKF9wXl1kpblq8yLhIzhXx5djqZbDGWYxhXKJf4QewFQ93bzaRx5j4iPWdY30GdA9Pr5EIdBUAAAAASUVORK5CYII=',
 		),
 		'chinaunionpay' => array(
			'machine_name' => 'ChinaUnionpay',
 			'method_name' => 'China Unionpay',
 			'parameters' => array(
				'method' => 'CC',
 				'brand' => 'CHINAUNIONPAY',
 			),
 			'not_supported_features' => array(
				0 => 'AliasManager',
 				1 => 'Recurring',
 				2 => 'HiddenAuthorization',
 				3 => 'ServerAuthorization',
 			),
 			'credit_card_information' => array(
				'issuer_identification_number_prefixes' => array(
					0 => '620',
 					1 => '621',
 					2 => '623',
 					3 => '625',
 					4 => '626',
 				),
 				'lengths' => array(
					0 => '16',
 					1 => '17',
 					2 => '18',
 					3 => '19',
 				),
 				'validators' => array(
				),
 				'name' => 'China Unionpay',
 				'cvv_length' => '3',
 				'cvv_required' => 'true',
 			),
 			'image_color' => 'data:image/png;base64,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',
 			'image_grey' => 'data:image/png;base64,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',
 		),
 		'visa' => array(
			'machine_name' => 'Visa',
 			'method_name' => 'Visa',
 			'parameters' => array(
				'method' => 'CC',
 				'brand' => 'VISA',
 			),
 			'not_supported_features' => array(
				0 => 'ServerAuthorization',
 			),
 			'credit_card_information' => array(
				'issuer_identification_number_prefixes' => array(
					0 => '4',
 				),
 				'lengths' => array(
					0 => '13',
 					1 => '16',
 				),
 				'validators' => array(
					0 => 'LuhnAlgorithm',
 				),
 				'name' => 'Visa',
 				'cvv_length' => '3',
 				'cvv_required' => 'true',
 			),
 			'image_color' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAAnCAYAAADEvIzwAAAK/0lEQVR42sXcXUwbyR0AcJL0EiVNT0RRJErjaLszaww2xMY2xthgJwYMoS1VFXSXu5IDUuJ8mBxxyPFxZ+KQAA4Q+5IQSBzugJArUqQ8VIpUqbzxhtQHv/GI1Ace/ejHaWfsHMV4d2d2veQs/aUIZf2xv52Z/8x/douMxo7jHNdVLBlmehQVRQ4WafA67bx9lDP3F9OiqKPjUO6RkYOcOVIsGd5sqPmep52xo7x7Qg/dE53QFY0J7kerwPVoHbim1oF7ah26p/8J66fGhYaZvwgN8XLY+vRIkSav/x6w/vHlMc67WLwTf84P6tvw8LNNHnyOeEjiEo4vEC+Q+BLHXxGvJ9GZjbLLOL7C0YV4A4nubJT34LiyajTeOF7ITwJVt7bA2a9RNvoRMJO4jSOEgIXEHRwDiFwIu4/jLHc9wDqIgG0IxzCOEQTsJL7NRs13OMKIYLF8D6838ivoHPdD18MkrJtA0EViEkcUQfejDzGFMCyOaRwzCDaQeIwjhqAnloSeeKfV+vITNedBfz7Rqm98mRIaE0hoevUhFpDQTOIHHD8iwb+Yicq2n05Q3zDTioXPHRz8IoaB0yqBSWzvPfmsL2jqAxgY0YB5853ZvccK1qEwA3CKpdUIzgcB6HyIYN34h1AFjCOO//70lOLz0DT3J6HxJcoGHVhofu1Q+hkHIPwMYNioCmAEy69E1bXevgQLMLCHdHnHWge3aMC8fXRW7vMNtREOOMe2oPMB0gqYXDBKzgHnjRcLjS+QEmDoXx4soNPsOATh5XaMu80KDCr+hpSOdRD2HcHAiAYMLXeSe4+1Wq9+ksGlAEPbaLvU5wv2iBnWjmFYEtoAA098TfFF7ns+phRY8C9taDDgdxzCLXmVFZgzdpUoAq7qu8gCzFtCvryr3jzEsQDrXPdKRXFrH5TD2vtIa2Ch4XFAyTkg4zX0zSMVwKgook2SW8TrL68xARt6PQqBNxmA02I9A7QNtrMAk8QpP/eIHMa4qX0B9sbNSs6B0DjXphaYu7BYogkwJ3SZWYB5Yy/zuABMN3SgKohowLj1hkSPt3+zRAOGjvCm6IXliMxmcZUCT/0buGa25IBL8RRHWff8YkstsL5lyacJMM62D7MAg4redeZeoTIYYwHOzH/FToz1mxQNmK/5bjB/3H96BDruIUbgJKgbN5EWL9a1ct6pEsEz04SB/5EFjqcU9Yy+WT3BVQ3sX4wWafXCwOsMwIhk4/QLJnIYVBJcCrDl9jux40utkWMYGFG7aFvYldcl1oy2MQL/S0k2TObaZfXfVynqnn1zq4UA49jSELirhwX4TOV16gScN19vYwEWqvvLRYcM+10zC/AZ99AJ0e6ZAbi8fvy3Rfv4MnqfH4e+OVQQcMsSUruoIjKl+QqwAHPGXmqSAU03NxmAJa9OUD0QYAEWa4EYd4OpBTsin+4nsP78fFAOWN/0KqVvXAjSgA0ti5wmX4hkoyzAoOKq7DRBZwmWgsqbiAYMLf0XpYEH1+jAI+/EEyyMywAsOKOBfdPF0xt4/nlKHjgRKm9eMNGAoX+5Xbtu2tD1ngaMM+lV2ayxMjjGAiyW2Py84gaqCa48MG8b6ZEA3mBNsnj3ZM9++IJzz1wYGMkBc97FEjKuU4FblmY1BO6+RG3Bxt60XC+QxZUHhpbbktkhtAyfYgHG/zZJTpHYs2gEXJNrsH7ilJbA8PzsBgV4Z3qnb1pIyQHrW5ZT2l15oFtH76J7kVThARqDfhZgnERJTuAzFSQGYKnvAGwRkxLgnXmwaypGsvdCz2FZ07PSDK4MMPS92hmecAtekgMWWpbR6Y63R7UyPsACzBt69eLJ1fUkDRie7ZddYxWsA2EG4JRc5Qi34qS6hQ4S0yGx1TEFrTdKAyYZ9s7/979qpwELba/LtRyHV2nAsCKQN/BzxhslwHQD0YC5qq9ly2Cg+u4WDZi3j8iOS8D+UKceeAqB+ultwTtlVnrujB2RwxgYUYBzihUkS6YBQ//rTu3Gj7LudmoLLr+aX7utvBZmAE7JVaQyFaTqu4gGDG3fUjNL3nnfp8FadEzRufM+vUgDLmuaz1mcITtFqC24eWVVM2BSMaIB46lS7hy2o+MQMF1PMwDLTk04cz/HAqyziFeQ8lpy7Zir4GKDe2aWtaqDs+dtGrBY94+Bt2SBW16nNU3zMW6aAoxIa9uBqbzuwcCIBkzb+gPNA+0swErGSGiZOAXrxjYLqia5H1OnKsA7b4LnCK40MPS9FH0fnElHKcAItr7RbmEGGHoSNGBdWbD0/8nVtQ0qcFX/EvVzLQNLNGBoH9lUs9GNdz68hIHTasuFBm+co7TedzRg3pcQTU4NjT/6acDChZ/M2nXThm4/DZivCPiyLaTvFDBdQzRg/mxITwWuHkjRgHnriOqtLJnie93DkKp6sCeWlHrfM+65E/DcMyQHrG98kZIqbpC6Lw1Y37oS1Az4d4bLJ2nAgvFqOFsWDAzSgGHVLWqry1SQqgcQtYu2DbsK/X1kBQkjR5UW/HdPb3KGAd+TEBW46UVIemPN20P0FvxmTeNxuCclBwyNgQ2SEQPjtTQNmDf3tVFbr23AxAJsqBk+qdlvdE/ogWtymxWY7I8Ww4HnnqZpwJz3uezuDH3zD0lZ4NYVxZv9aN10TA4YGANkutSGgRENmCUpAmcHAizAmv7ITPc6eQIDp5m27NTH8wok/LknPgyMaMBqy4W7gQ2+5ZNaAntowDjSNGDh7K0wU2JXPfCeAfj9fhQHoDsaYtuTNdOZP/d9kvxYwGWtb1za/Wj45acMwIgGbKi5yXLVHSB3NtCBh/elxIcTrotsLTiWs+kQNDzWYWD0sYBh68qgtuNwec92gcBMiQEZV9mAxStI2dUx9VtMgSu6xgK8d6qEYRMfE1i48HpTU+Dfl/WMFQRcecvKdIJt/S4WYKlqD2cfMUPHaIp3jl4yeiOK7qMSXNEAY5KV3r2ilcnECe7HBCaJFk7qtBuHK7odBQCTig9TQiRYQmEqsH04JX2BhIMYGP28mwM4ImvQcb+Tr7mnL3M9+k3uSYkcrGqe/jXvHPcB18Q6+zQpnlPD5j3xS78EMHfhbYlmwKXWq8fUAkPTLeYKCLTc2aQDDyYkgR3htd3AqurBtJUs3+zJnJKk9/vULwEs+FfaNB6Hr2ypAWa9GzFTQfpw+6gcMLnTQWoZktxCup/AfMPjnK095O4GDIyowI1zUb5xvocEzIlED2xeyA//wiANWN+6EtUaeFA5cDDBnODYQzoWYGD/Vie5xWcfgUHDzPu9c2/gebLGAJxSM2cXmpfSsi249c2WpsBkm6xiYNMNHXP3bL7dzgK8u3qVcwHaR337BQzcGHdPqZB01dBDcGnAc6qmNELz4ioFGGn3tIGiD7e1KACGlTeTiqYo1aEEDRjahjalx9/Rsf0A1tdPiy7uC554mAWYtjQpecH7FztpwIaWVU7jdekrSVZgvuqmohumgCWUogEL9iHJ1TBYE05qCQzc0+/I8qXYZ2UeAeGJp2nA4Pxz1d0of2FZT23Bf3hzUdv5sOFKkBE4nf8gFfksffczOqS7aPkKEnDegxh6BDoi/1EDDFzRbYzbSbvbQaifbss8woHagudV76E67Xx7lAYstP59SVNgcuXmPoFH/Ck5BEzZO5On6Ii9V+7TdJSsUmXvLoycJtt1hNpIl+B4EIbO+zFY+yABneMxUibEc+AeWBdtN9ROckrGs9LM03HixbQo9MZtsafu7A6j9+3x/wHJwwyjqYbGUgAAAABJRU5ErkJggg==',
 			'image_grey' => 'data:image/png;base64,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',
 		),
 		'vpay' => array(
			'machine_name' => 'Vpay',
 			'method_name' => 'V PAY',
 			'parameters' => array(
				'method' => 'CC',
 				'brand' => 'VPAY',
 			),
 			'not_supported_features' => array(
				0 => 'ServerAuthorization',
 			),
 			'credit_card_information' => array(
				'issuer_identification_number_prefixes' => array(
					0 => '4026',
 					1 => '417500',
 					2 => '4405',
 					3 => '4508',
 					4 => '4844',
 					5 => '4913',
 					6 => '4917',
 				),
 				'lengths' => array(
					0 => '13',
 					1 => '14',
 					2 => '15',
 					3 => '16',
 					4 => '17',
 					5 => '18',
 					6 => '19',
 				),
 				'validators' => array(
					0 => 'LuhnAlgorithm',
 				),
 				'name' => 'V PAY',
 				'cvv_length' => '3',
 				'cvv_required' => 'true',
 			),
 			'image_color' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC4AAAAyCAYAAAAjrenXAAAENElEQVR42u2YT0gUURzH36GDBw8ePOTqwUOhBwkDDx6ChP7YrUh2Zy1hpSAJqy0qJCosDCWWsg5ppeubtB0lI40koyilf0b/DC2MrMTMNlzESnHZ1Zjm92zGmdl562Yzu7PgD34s+97jzef3e9/5vfcGIbDtdWnIxhagQpyJ7DjdlG6tX4GKcQKSzHohEVnda1A8WN6JZUIQeXN/FFHEgTE4lygD2d05KN6MZF1KvbYFmi1MkEt1LOQBzpIZccZsDQ4Nz9cXnEspEMCagpxlQnA+nAc8FtfC4Gy/AM+HuB2X6QouGo/TE+aya+kNAz8TNvMgSy1ohp1BVrzcEHD1KkxfTRvRhk91hJFJtTY4btZd4zRLLLrMXT+z4Y0GeDW1lDGsjyKTvKiBI6ahBB5afHjPl4AndUrSOWfppIzfTMn2gCFVhWpQS/8+fG3p8XEZvI8ikzYKuDO64HMwXhEgf9+RYXg5AZ7nLMnKIC8lC2P8GtB+tAUnxQK8WQ5y/lTBawCf8aQoa7KdLaVkGxu2AUWic7l/Y1e+DXpSnKra/YICnhsbcCvOVsOsLqkc9nNp7nno+iwKdK+hW34EcplQQ7W41ntk/VXa4MJqxRg8pFqs2lU5INVuGzuisVNOkuN0jMGdWhm17DidQQ5O2htOreGnw8XonDjcqOxsk7ZMBN3HHJyicwHcRandPVE5jy9W5xRonpy9TQTupJQ89Uvp0+WqqBs4Teda8onW1e2/dK52csk1H3jbAjK5F9XLsm46h/JoSvDwOveSXdSU4GA23ClAdmm4E+lpuoOb6YPQErju4AzeatpPyzRn2I2I3EQi2q5N5IV4/xK4qcEfDXznh8YmiQ96f/Jtz4f5NeW3Q8aVeV7yYI6aR1IbjJuYCvD5lXdDxmceuEHm3HnxsTHgMDnAA1BVex8/MPqDwCQ6rirGQVAzs7/5ng9jIYFDX0JRo6L9Xt8omWvZtivGgePuQel/2u5rBLCk7qnUlneyk2T7YNNz8gvZFPuyDrWT8bAiYttm130ybl3FHeOkogaHDPl++RVtTQ8/ksxCn3dimnfd6lfMASs16Q/yy0taSOZhzuYnn43VuBwcHlrq7iHZEjOetMPD+4OzklYBGgKTSwNkNTI+RQI82vKKBAErZzg4ZBEyCg8EgxdU1CYEAuCi5kEmYAVnHyjmYc51k3YYK5eNoeC9Q+N8eWsvyXLusQ5F/4tPPhIYrIroANf55iu1QkX8QuqpcbnnHLlFstj6bEgBDhUDLH1Pq2I89HW980anjocDr737nvSr20E2kHVYJdOBw8sH9VwNJ4eEF1Iui6iCgy7V5U2sxRCUWg7y2g792WU3pTaY55/LoALczr6Mu7MKg/dCxhvjDryoJkO4nddvEg7mU3EDbW/g5q9CRe4MxDTUCAE8Jl9Tzeg23CHIukL89vgHzoIDL8MYRRsAAAAASUVORK5CYII=',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC4AAAAyEAAAAABWVmpIAAADc0lEQVR42t2WbUhTURjHL5S4T9FHQT+sb4OUQCLIMOhFE4eOwVTUiBhZynComUMEDcvIpLDExJdKjAsOY76kow1R0RSmm6CXHIhmXBtcuaRYg9FanDwdj+f6Mt29u/tQz//Dznl27++e/ffccx4KAN42oWR5ziyfPGV+DdgKyhfLTAHZI+Ca023B0TPkD7eC5alFBYhQzOkouHxhfB1lE/bqx82DVmZPxppZDhHumXR+Gvzc694tZm7vrQUjKSYs8/kQ4TB+G9iEkW9CeH/M7tUvKgg67f66TgQc/warneDZBOF3LbcIvG4hZM+F8eTe2wc7xsQJC02XSODBCEfAB9tTTNXWgWcQPnWZ5KfiCVpvE1EtwmB5eLuhAeKtSpKvCRC4xSkRDkBOFARUZPXH9Lp/Mii3eUetxWi11quQDK9bQJC25V73WiPKDUSRdTf0iXqJ9ruOZK1c2jbA0ELgbkUY8CUzxhjPzF6AmZUxgi5sEvn67w3taYwaOg7nry4S+GB7mHBSGXcLYYXn1uF5Zq4vNky4xUlW6rk6s0xmz1dF74rBXU8xTSjrz5HZyljYcKHrbSdIhRuLJeznh7lO0Ckme7IscKHrWLrEo4/HkOBC14lBko65w13HYnnZ4MJdEMqULvGADsX1CaWM8N2u50QFXDLCAagcKr+GFfx4kAiXqSn6d+Afj8nZPAvlnKcKm/a/f/LI8vK/gzMOjuZoT9mkttSCcx0ZADQkwVGpxRtd2bjTc9Vy9NMPIuAczTgakrpTWa83OjNv+5ArC7gWfuGHe8rUKjSejWG9aWZRcNsG/MwdDrga/zZ15fEAtPYDoK+Fs4KhgKsjA45qLgFQcUOULRieZt40oNGwkXGkmdeze/LRFd2pvricU2oVR492ivQcwdWqpiIA4Mq1JX4N9LUnf9OA7MjM4+3Dxjd6X1zusGj4ejbj8G315JNa6GdTkV8DvdfXAlCbg655+B0AvwaZIxK+9K6ruZE3VqH54sn1bNsGlF8z0yqsqYP+yhA9RzLcBmDciuCzMQBcn0d528ZcuoQ63w1/383RO01cnl/T1SwbXK3yRmMcQvJ2ZIZEOOPAJQcrmaOxEajiObpwGo568oOV4TbckBWpvaXvClVfHSn46jg17cl4HQn0oy8AUACssi/OlgwZi+VT1VrnY9hL/gGAg6AHaFfZmgAAAABJRU5ErkJggg==',
 		),
 		'paydirekt' => array(
			'machine_name' => 'Paydirekt',
 			'method_name' => 'paydirekt',
 			'parameters' => array(
				'method' => 'OT',
 				'brand' => 'PAYDIREKT',
 			),
 			'not_supported_features' => array(
				0 => 'AliasManager',
 				1 => 'HiddenAuthorization',
 				2 => 'ServerAuthorization',
 				3 => 'Recurring',
 				4 => 'Moto',
 				5 => 'Cancellation',
 			),
 			'image_color' => 'data:image/png;base64,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',
 			'image_grey' => 'data:image/png;base64,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',
 		),
 		'debitmastercard' => array(
			'machine_name' => 'DebitMasterCard',
 			'method_name' => 'Debit MasterCard',
 			'parameters' => array(
				'method' => 'CC',
 				'brand' => 'MASTERDEBIT',
 			),
 			'not_supported_features' => array(
				0 => 'ServerAuthorization',
 				1 => 'HiddenAuthorization',
 				2 => 'Moto',
 			),
 			'credit_card_information' => array(
				'issuer_identification_number_prefixes' => array(
					0 => '510259',
 					1 => '510782',
 					2 => '510840',
 					3 => '510875',
 					4 => '514700',
 					5 => '517869',
 					6 => '518868',
 					7 => '519463',
 					8 => '5141',
 					9 => '5179',
 					10 => '5236',
 					11 => '5262',
 					12 => '5264',
 					13 => '526418',
 					14 => '526471',
 					15 => '526495',
 					16 => '526790',
 					17 => '527432',
 					18 => '5275',
 					19 => '528013',
 					20 => '529964',
 					21 => '531445',
 					22 => '532700',
 					23 => '539738',
 					24 => '5399',
 					25 => '539923',
 					26 => '539941',
 					27 => '539970',
 					28 => '541592',
 					29 => '541597',
 					30 => '542432',
 					31 => '5443',
 					32 => '544440',
 					33 => '544927',
 					34 => '545045',
 					35 => '548901',
 					36 => '548912',
 					37 => '548913',
 					38 => '554827',
 					39 => '557071',
 					40 => '557300',
 					41 => '557361',
 				),
 				'lengths' => array(
					0 => '16',
 				),
 				'validators' => array(
					0 => 'LuhnAlgorithm',
 				),
 				'name' => 'Debit MasterCard',
 				'cvv_length' => '3',
 				'cvv_required' => 'true',
 			),
 			'image_color' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD0AAAAyCAYAAADvNNM8AAAElklEQVR42u2ZP0wTURzHH4h/U6QGkHp3xMYwEGVA4+BA4kUHB0wYHDTBiEljSCSmJsYwOKBxINFEBvyTWNobHBg6EMPg0MGBwYGBgYGhA4MDkVarAu3Vozx/r/0dnqXCcfeDGr1f8g1H3rvX93m/9+79fu8x5plnnnnm2X9qaSa1LzIplGLSECiSZrKWYsrTNFMG4a/KGauz0w5/0ezTY62Xcpo0lI0pozlN1op/Y/KDbEy6mo3ISlVBF1kwAKDDKSYnQXwLLYHGF5ncWaktfUzuBrAEyADxLfRB15Qw14IHdhG22VfyaBGEO9DkZ1by2LImd+Zi0rQN0Er6COrbceAFFgiCt2Ydwq4rXSdljTv1b6HTBYfA69KjyoRYFjvkYakLPLzgFjhVAx1VD3Lex7gxVM/dQqNml7VggBhYaYMOp1wDg/TzJWBTxuMGEnA9Js+Qeby0huU5CuDlkw2/ARd1k/H8SDMVeJwEGjo7QgH8pb6Fr12v2QgNWuvfw3NjEgm42NoopnWOZFqrBysCr0/zR4ep1neSP7QXF/zBy1KEysubARe9fQu8HZWppnm/I2ARRVF9vFZO128JLfTjSSOVtxMOvayoFMBCxuV9tqBXBw9RQRuOvuQQhDygAE7vkWwBF6f47b1U0Dwfk3ocJBEiaXAPnWlstg0tti8qaEfrGj5i7yigvwaa7EOD9NfHaMAhU3OyP7+vCvTLAI2nNXnEyfSOVwOaatvKaspg9SIxX8D+hyxUS7amHaWdaSYNUG1Za701tqAL4f1k0CvR1rPbhhaJPhV0/sIBW9DGEFUoKi24STZmKKC/nzhi7yM2epQKOuIGup8qQClcq921qS2OoRxDY/ydJMmlTzVsCp1/1kQDHZXHKfLpbpK1XSPz1Z69lWPu+2Qx95I+prRRnY8Nk2xf/qMbvuTiAIEqCnN9gFBhmk9SgH9TGjm/8SuH1p+3VC/stAdOc6jwTWosFEJ1P/QXLWsUaWQ2Kt/b4cN+Jez2CEmEuCt3j58RtxVu92NxDbRrVzrodWObwDOf2K9OirMskQLibcV20saMmM47dsi/VdSG4WpiE+/PwewYhbrn/rh0AF4k/QD0ZpMBWBLbUS6q9Ga0oP+vub3MsKBf3GCKo6bSTaYzTwio/FhrR05TVLEFVcWjnjkzETB07sLvDFL9jrgYC2LHzUP0jgpQ4r7YXLfiWaR2PnweBmmWcrMNs712UBO+Y75v7bwf+9COz+X1zXLxGyoFdF/xlpaxWdAcaBr/TmK5yGiSqHkEmcMOTOCgTYFmQE/xnWl8fx4HRsP3xWHexWL0y5iIocOgbtASvp9CKLP+AJZnsM0UJfQ4wkxhx8SluoGjnUSwLnyuQ8/G0SNmG2bEpGInzY734nMIy+NY37QE1vEhmFpW/x3Wt5aTQJsdtk4fjoAfQVdAI+g5c+p2ocf9WB7HjilYT0Wv+svaNQfMj3Vf4f8qelwtqz9aody1XbKMvPVD8d5SnkDocfR+BMuHsY4Pp3oCByqE5RGsb203gG1N4WAFsd4EwnaW1TcHZtJS7plnnnnm2T9nPwFAFOS9Sg2C0QAAAABJRU5ErkJggg==',
 			'image_grey' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD0AAAAyEAAAAACaz1CjAAAEXElEQVR42u3Yf0wbVRwA8MbwxzCNIYElJA7SxDrvj6XpH51uy8UU0j8oNLHOklYJ9RIuSOxi6rJldckytIcFmQRwPzrTSBuLOqhIhOF+dDuRbGRrHMjEklRSpRmHlHCaQy/ZWU5eagPH9fqu1USN3PvrvX7vPu/de/d+VMH/Y5dih96h/xV0wnF1V39FT2mXy/fR4O6ZqpRGHMNFlupme++5767dc3/36ULgt8a/SK/G+g41vWwMCNPh/e3d877NqMXOcc3g0IBVmG7sjT2ZchREs/b+isP7t7Ob6TS3Uszz9PT1L7ajm2nk5g+786aXllvmpNk/W08MPzP4iDScTrfKuUge9Lepl4ZhcO2B17WE50IIRg9Yr1ayv8qkFw9aV2GwMeC8SHhAer8ajl+7nK3lCnEfN6NwuOlyGiY8bZWBV+H47XEZ9MVrcPj562+9l6EJT4fxkhKOLwQg9OLB516E06CXtyYvC6fHXltvzEn3lMppsxAmPO2PX/oSjs8TOeiURs4Ae4XaThOeD96B0+OaHPRMFRw2BlzfiOneZTg9OCQc5wL64wU4XHdODBOezho4PWB94JOku1xwut6ajW6rlEMLe1tAn3oUTtveyEYTnk8eg9OzvZL0iebC6f5zcHr6uCTdRhVOy/m85p6WpGXNZNXZ4Len5fS1cAkV0CNn5Hxcb/4uprsQOfRqvSS9UiyHPvqumL5wBw5/bso5kTrW4LS9T0x/eB5OR77PSY/Vy5lUWncV8rrp6Zx0SiPeBooTbhLS/jE4POmGrtd3G+B07YGTtzbhnjAcHrIzERkbpL5DcPyFpsw47zDKmcfEG4WsdEpzmoPjDUXun8FKHazNdwLNuSNNaeRsGRqe6KjsR+FL5Ryb5xFg2APbKrVRP8Vu7IV9y0t1BRx8VmM9paZT2VnH2tcVIGa9cZ4YuZmd/Yyf7ZXa/ss47q0Uj5w5Wb21/c3o+a/mSrfGrDc+8N15dmsFhuyT7h+rH3b+LYfctYGEY6Yq4WDt0jEPO395ajnERHK19L97tI/Fpqbyvae9XXyPiKaoeDwW4ziev38/Q7Hs5OTGkYiNRBiGZV0uDAN5EAHiotFkMhIBv6cfT9PxeDRK05lykMcwkoTSfn95+b59CKLTIYhpY5nDcfXGpVJxHIJgmNlMUSiq1R47xvM6ncmkUjEMhqnVXm84XFZms3V3j44qlVptWRlJgvKzZ0dHS0p0OpCXQdtsHIeiXm8iUVSUTKrVFDUxoVZznMtlsUSjIKK1ledJsqQEPDwYxDDfxibXYvH7wf0GQzDIMDodoEF5TY3fn87LoMGD0y9IoeC4PXtCIadTpQKvd2ICQWg6FLJYGCaRUKlIMhym6XQsqBhNJxItLRYLSSqVgAblR45k8lD6yhVQ+/Sw0OtB3mBwOm22ZBLH9XqXi+cZxmw2GDjO59PrcTyZTMdSlNOJoqFQPI7jZjOGTU2ly0FlTCaQ3/nfbIf+f9F/AOHCDwE/4NgTAAAAAElFTkSuQmCC',
 		),
 		'debitvisa' => array(
			'machine_name' => 'DebitVisa',
 			'method_name' => 'Debit Visa',
 			'parameters' => array(
				'method' => 'CC',
 				'brand' => 'VISADEBIT',
 			),
 			'not_supported_features' => array(
				0 => 'ServerAuthorization',
 				1 => 'HiddenAuthorization',
 				2 => 'Moto',
 			),
 			'credit_card_information' => array(
				'issuer_identification_number_prefixes' => array(
					0 => '4',
 				),
 				'lengths' => array(
					0 => '16',
 				),
 				'validators' => array(
					0 => 'LuhnAlgorithm',
 				),
 				'name' => 'Debit Visa',
 				'cvv_length' => '3',
 				'cvv_required' => 'true',
 			),
 			'image_color' => 'data:image/png;base64,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',
 			'image_grey' => 'data:image/png;base64,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',
 		),
 		'creditcard' => array(
			'machine_name' => 'CreditCard',
 			'method_name' => 'Credit Card',
 			'parameters' => array(
				'method' => 'CC',
 				'brand' => '',
 			),
 			'not_supported_features' => array(
				0 => 'ServerAuthorization',
 			),
 			'image_color' => 'data:image/png;base64,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',
 			'image_grey' => 'data:image/png;base64,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',
 		),
 	);

	/**
	 *
	 * @var Customweb_PayUnity_Configuration
	 */
	private $configuration = null;

	/**
	 *
	 * @return Customweb_PayUnity_Configuration
	 */
	public function getGlobalConfiguration(){
		return $this->configuration;
	}

	/**
	 *
	 * @param Customweb_PayUnity_Configuration $configuration
	 * @return Customweb_PayUnity_Method_DefaultMethod @Inject()
	 */
	public function setGlobalConfiguration(Customweb_PayUnity_Configuration $configuration){
		$this->configuration = $configuration;
		return $this;
	}

	protected function getPaymentInformationMap(){
		return self::$paymentMapping;
	}

	/**
	 *
	 * @return string|null
	 */
	public function getPaymentMethodBrand(){
		$params = $this->getPaymentMethodParameters();
		if (isset($params['brand'])) {
			return $params['brand'];
		}
		else {
			return null;
		}
	}

	/**
	 *
	 * @return string
	 */
	public function getWidgetStyle(){
		return $this->getPaymentMethodConfigurationValue('widget_style');
	}

	/**
	 * This method is called to validate the payment information provided by the customer.
	 *
	 * @param Customweb_Payment_Authorization_IOrderContext $orderContext
	 * @param Customweb_Payment_Authorization_IPaymentCustomerContext $paymentContext
	 * @param array $formData
	 */
	public function validate(Customweb_Payment_Authorization_IOrderContext $orderContext, Customweb_Payment_Authorization_IPaymentCustomerContext $paymentContext, array $formData){
		$this->getChannelId($orderContext->getOrderAmountInDecimals());
	}

	/**
	 *
	 * @param double $orderAmount
	 * @return string
	 * @throws Exception
	 */
	public function getChannelId($orderAmount){
		$channelId = trim($this->getPaymentMethodConfigurationValue('channel_id'));
		if ($this->existsPaymentMethodConfigurationValue('channel_conditions')) {
			$channelConditions = trim($this->getPaymentMethodConfigurationValue('channel_conditions'));
			if (!empty($channelConditions)) {
				$conditions = explode("\n", $channelConditions);
				foreach ($conditions as $condition) {
					$parts = explode(';', $condition);
					if (count($parts) != 3) {
						throw new Exception(
								"Invalid format of the channel conditions. The condition '" . $condition . "' does not contain exactly 2 semicolons.");
					}
					$lower = floatval(trim($parts[0]));
					$upper = floatval(trim($parts[1]));
					if ($lower <= $orderAmount && $orderAmount < $upper) {
						$channelId = trim($parts[2]);
					}
				}
			}
		}
		if (empty($channelId)) {
			$channelId = $this->getGlobalConfiguration()->getGlobalEntityId();
		}

		return $channelId;
	}

	/**
	 *
	 * @return string
	 * @throws Exception
	 */
	public function getNoThreeDChannelId(){
		$channelId = trim($this->getPaymentMethodConfigurationValue('channel_id_nothreed'));
		if (empty($channelId)) {
			throw new Exception(
					Customweb_I18n_Translation::__('For recurring and moto transactions to work, you have to specify a separate channel.'));
		}
		return $channelId;
	}

	/**
	 *
	 * @param string $capturingMode
	 * @return string
	 */
	public function getPaymentTypeCode($capturingMode = null){
		if ($capturingMode == null && $this->existsPaymentMethodConfigurationValue('capturing')) {
			$capturingMode = $this->getPaymentMethodConfigurationValue('capturing');
		}
		if ($this->isDirectCapturingSupported() && $capturingMode == Customweb_Payment_Authorization_ITransactionContext::CAPTURING_MODE_DIRECT) {
			return Customweb_PayUnity_IConstants::PAYMENT_TYPE_DEBIT;
		}
		elseif ($this->isRiskBasedCapturingSupported() && $capturingMode == 'riskbased') {
			return Customweb_PayUnity_IConstants::PAYMENT_TYPE_RISK_BASED;
		}
		else {
			return Customweb_PayUnity_IConstants::PAYMENT_TYPE_PREAUTHORIZATION;
		}
	}

	/**
	 *
	 * @return boolean
	 */
	public function isDirectCapturingSupported(){
		return true;
	}

	/**
	 *
	 * @return boolean
	 */
	public function isRiskBasedCapturingSupported(){
		return true;
	}

	/**
	 * @param Customweb_I18n_LocalizableString $gatewayErrorMessage
	 * @param Customweb_I18n_LocalizableString $detailErrorMessage
	 * @param Object $response
	 * @return Customweb_Payment_Authorization_ErrorMessage
	 */
	public function getErrorMessage($gatewayErrorMessage, $detailErrorMessage, $response){
		if ($detailErrorMessage == null) {
			$detailErrorMessage = Customweb_I18n_Translation::__("The transaction was declined.");
		}
		return new Customweb_Payment_Authorization_ErrorMessage($detailErrorMessage, $gatewayErrorMessage);
	}

	/**
	 *
	 * @param Customweb_Payment_Authorization_IOrderContext $orderContext
	 * @param Customweb_Payment_Authorization_ITransaction $aliasTransaction
	 * @param Customweb_Payment_Authorization_ITransaction $failedTransaction
	 * @param Customweb_Payment_Authorization_IPaymentCustomerContext $customerPaymentContext
	 * @param string $authorizationMethod
	 */
	public function getVisibleFormFields(Customweb_Payment_Authorization_IOrderContext $orderContext, $aliasTransaction, $failedTransaction, $customerPaymentContext, $authorizationMethod){
		return array();
	}

	/**
	 *
	 * @param Customweb_PayUnity_Authorization_OppTransaction $transaction
	 * @param array $formData
	 * @return array
	 */
	public function getAuthorizationParameters(Customweb_PayUnity_Authorization_OppTransaction $transaction, array $formData){
		$parameters = array();
		$parameters['merchantTransactionId'] = $this->getMerchantTransactionId($transaction);
		return $parameters;
	}

	public function getDescriptor(Customweb_PayUnity_Authorization_OppTransaction $transaction){
		return $this->applySchema($transaction, $this->getGlobalConfiguration()->getDescriptorSchema(), 127);
	}

	public function getMerchantTransactionId(Customweb_PayUnity_Authorization_OppTransaction $transaction){
		return $this->applySchema($transaction, $this->getGlobalConfiguration()->getTransactionIdSchema(), 255);
	}

	/**
	 * Applies order schema. Before using util also replaces {tid} with transaction id (not external), and {oid} with order id
	 * @param Customweb_PayUnity_Authorization_OppTransaction $transaction
	 * @param string $schema
	 * @param int $maxLength
	 * @return string
	 */
	protected function applySchema(Customweb_PayUnity_Authorization_OppTransaction $transaction, $schema, $maxLength){
		$applied = false;
		if (strpos($schema, "{tid}") !== false) {
			$schema = str_replace("{tid}", $transaction->getTransactionId(), $schema);
			$applied = true;
		}
		if (strpos($schema, "{oid}") !== false) {
			$schema = str_replace("{oid}", $transaction->getTransactionContext()->getOrderId(), $schema);
			$applied = true;
		}
		if ((strpos($schema, "{id}") !== false || strpos($schema, "{ID}") !== false) || !$applied) {
			$schema = Customweb_Payment_Util::applyOrderSchemaImproved($schema, $transaction->getExternalTransactionId(), $maxLength);
		}
		return $schema;
	}

	/**
	 *
	 * @param Customweb_PayUnity_Authorization_OppTransaction $transaction
	 * @param array $items
	 * @return array
	 */
	public function getCaptureParameters(Customweb_PayUnity_Authorization_OppTransaction $transaction, array $items){
		return array();
	}

	/**
	 *
	 * @param Customweb_PayUnity_Authorization_OppTransaction $transaction
	 * @param array $items
	 * @return array
	 */
	public function getRefundParameters(Customweb_PayUnity_Authorization_OppTransaction $transaction, array $items){
		return array();
	}

	/**
	 *
	 * @param Customweb_Payment_Authorization_ITransaction $transaction
	 * @param stdClass $response
	 * @throws Customweb_Payment_Exception_PaymentErrorException
	 */
	public function validateResponse(Customweb_Payment_Authorization_ITransaction $transaction, $response){
		if (!isset($response->amount) ||
				!Customweb_Payment_Util::amountEqual($transaction->getTransactionContext()->getOrderContext()->getOrderAmountInDecimals(),
						$response->amount, 2)) {
			throw new Customweb_Payment_Exception_PaymentErrorException(
					new Customweb_Payment_Authorization_ErrorMessage(Customweb_I18n_Translation::__('The response seems not to be valid.'),
							Customweb_I18n_Translation::__('Validation: The amounts do not match.')));
		}
		if (!isset($response->currency) || $transaction->getCurrencyCode() != $response->currency) {
			throw new Customweb_Payment_Exception_PaymentErrorException(
					new Customweb_Payment_Authorization_ErrorMessage(Customweb_I18n_Translation::__('The response seems not to be valid.'),
							Customweb_I18n_Translation::__('Validation: The currencies do not match.')));
		}
		if (!$this->validatePaymentType($transaction, $response)) {
			throw new Customweb_Payment_Exception_PaymentErrorException(
					new Customweb_Payment_Authorization_ErrorMessage(Customweb_I18n_Translation::__('The response seems not to be valid.'),
							Customweb_I18n_Translation::__('Validation: The payment types do not match.')));
		}
	}

	/**
	 *
	 * @param Customweb_Payment_Authorization_ITransaction $transaction
	 * @param stdClass $response
	 * @return boolean
	 */
	protected function validatePaymentType(Customweb_Payment_Authorization_ITransaction $transaction, $response){
		if (!isset($response->paymentType)) {
			return false;
		}
		$paymentTypeCode = $this->getPaymentTypeCode($transaction->getTransactionContext()->getCapturingMode());
		if ($paymentTypeCode == 'PA.CP') {
			if (!($response->paymentType == 'CP' || (isset($response->workflow) && $response->workflow == 'PA.CP'))) {
				return false;
			}
		}
		elseif ($paymentTypeCode != $response->paymentType) {
			return false;
		}
		return true;
	}

	protected function getPhoneNumberElements(Customweb_Payment_Authorization_IOrderContext $orderContext, $customerPaymentContext, $canHide = true){
		$paymentCustomerContext = $customerPaymentContext->getMap();
		$elements = array();
		$phoneNumber = $orderContext->getBillingAddress()->getPhoneNumber();
		$mobileNumber = $orderContext->getBillingAddress()->getMobilePhoneNumber();
		if (empty($phoneNumber) || !$canHide) {
			$default = '';
			if (!empty($phoneNumber)) {
				$default = $phoneNumber;
			}
			elseif (!empty($mobileNumber)) {
				$default = $mobileNumber;
			}
			elseif (isset($paymentCustomerContext['phone']) && !empty($paymentCustomerContext['phone'])) {
				$default = $paymentCustomerContext['phone'];
			}
			$control = new Customweb_Form_Control_TextInput('phone_number', $default);
			$control->addValidator(new Customweb_Form_Validator_NotEmpty($control, Customweb_I18n_Translation::__("Please enter your phone number.")));

			$element = new Customweb_Form_Element(Customweb_I18n_Translation::__('Phone Number'), $control,
					Customweb_I18n_Translation::__('Please enter here your phone number.'));
			$elements[] = $element;
		}

		return $elements;
	}

	protected function getBirthdayElements(Customweb_Payment_Authorization_IOrderContext $orderContext, $customerPaymentContext, $canHide = true){
		$paymentCustomerContext = $customerPaymentContext->getMap();
		$elements = array();
		$birthdate = $orderContext->getBillingAddress()->getDateOfBirth();
		if ($birthdate === null || !($birthdate instanceof DateTime) || !$canHide) {
			$defaultDay = '';
			$defaultMonth = '';
			$defaultYear = '';
			if ($birthdate != null && ($birthdate instanceof DateTime)) {
				$defaultDay = $birthdate->format('d');
				$defaultMonth = $birthdate->format('m');
				$defaultYear = $birthdate->format('Y');
			}
			elseif (isset($paymentCustomerContext['birthDate']) && ($paymentCustomerContext['birthDate'] instanceof DateTime)) {
				$date = $paymentCustomerContext['birthDate'];
				$defaultDay = $date->format('d');
				$defaultMonth = $date->format('m');
				$defaultYear = $date->format('Y');
			}
			$elements[] = Customweb_Form_ElementFactory::getDateOfBirthElement('date_of_birth_year', 'date_of_birth_month', 'date_of_birth_day',
					$defaultYear, $defaultMonth, $defaultDay);
		}
		return $elements;
	}

	protected function isDateOfBirthValid(array $formData){
		return isset($formData['date_of_birth_year']) && isset($formData['date_of_birth_month']) && isset($formData['date_of_birth_day']) &&
				!empty($formData['date_of_birth_year']) && !empty($formData['date_of_birth_month']) && !empty($formData['date_of_birth_day']);
	}

	protected function getCompanyUIDElement(Customweb_Payment_Authorization_IOrderContext $orderContext, $customerPaymentContext){
		$paymentCustomerContext = $customerPaymentContext->getMap();
		$elements = array();
		$default = '';
		if (isset($paymentCustomerContext['companyUID'])) {
			$default = $paymentCustomerContext['companyUID'];
		}
		$control = new Customweb_Form_Control_TextInput('company_uid', $default);
		$elements[] = new Customweb_Form_Element(Customweb_I18n_Translation::__('Company VAT Number'), $control);
		return $elements;
	}

	protected function getGenderElements(Customweb_Payment_Authorization_IOrderContext $orderContext, $customerPaymentContext, $canHide = true){
		$paymentCustomerContext = $customerPaymentContext->getMap();
		$elements = array();
		$gender = $orderContext->getBillingAddress()->getGender();
		if ($gender === null || empty($gender) || !$canHide) {
			$default = 'male';
			if ($gender != null && !empty($gender)) {
				$default = $gender;
			}
			elseif (isset($paymentCustomerContext['gender']) && !empty($paymentCustomerContext['gender'])) {
				$default = $paymentCustomerContext['gender'];
			}
			$control = new Customweb_Form_Control_Radio('gender',
					array(
						'male' => Customweb_I18n_Translation::__('Male'),
						'female' => Customweb_I18n_Translation::__('Female')
					), $default);
			$elements[] = new Customweb_Form_Element(Customweb_I18n_Translation::__('Gender'), $control,
					Customweb_I18n_Translation::__('Please enter here your gender.'));
		}
		return $elements;
	}

	public function getAdditionalWidgetOptionString(Customweb_Payment_Authorization_ITransaction $transaction){
		return '';
	}

	public function getCartItemParameters(Customweb_PayUnity_Authorization_OppTransaction $transaction){
		$parameters = array();
		return $parameters;

		//Disable cart until ____paymentServiceProvider____ allows discounts.
		/*
		 * $i = 0;
		 * $orderContex = $transaction->getTransactionContext()->getOrderContext();
		 * foreach ($orderContex->getInvoiceItems() as $item) {
		 *
		 * if (round($item->getQuantity()) == 0) {
		 * continue;
		 * }
		 * $name = $item->getName();
		 * if(empty($name)){
		 * $name = Customweb_I18n_Translation::__('No Name Provided');
		 * }
		 *
		 * $parameters['cart.items[' . $i . '].name'] = Customweb_Filter_Input_String::_(strip_tags($name), 255)->filter();
		 * $parameters['cart.items[' . $i . '].merchantItemId'] = Customweb_Filter_Input_String::_($item->getSku(), 255)->filter();
		 * $parameters['cart.items[' . $i . '].quantity'] = round($item->getQuantity());
		 * $parameters['cart.items[' . $i . '].type'] = $item->getType();
		 * if ($item->getType() == Customweb_Payment_Authorization_IInvoiceItem::TYPE_DISCOUNT) {
		 * $price = -1 * $item->getAmountIncludingTax() / round($item->getQuantity());
		 * } else {
		 * $price = $item->getAmountIncludingTax() / round($item->getQuantity());
		 * }
		 * $parameters['cart.items[' . $i . '].price'] = Customweb_Util_Currency::formatAmount($price, $orderContex->getCurrencyCode());
		 * $parameters['cart.items[' . $i . '].currency'] = $orderContex->getCurrencyCode();
		 * $parameters['cart.items[' . $i . '].tax'] = number_format($item->getTaxRate(), 1);
		 * $i++;
		 * }
		 * return $parameters;
		 */
	}
}
