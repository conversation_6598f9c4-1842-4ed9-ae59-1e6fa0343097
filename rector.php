<?php

declare(strict_types=1);

use <PERSON>\CodeQuality\Rector\Class_\InlineConstructorDefaultToPropertyRector;
use <PERSON>\Config\RectorConfig;
use <PERSON>\Set\ValueObject\LevelSetList;

return static function (RectorConfig $rectorConfig): void {
    $rectorConfig->paths([
        __DIR__ . '/src'
    ]);

    // register a single rule
    $rectorConfig->rule(InlineConstructorDefaultToPropertyRector::class);

//     define sets of rules
    $rectorConfig->sets([
        LevelSetList::UP_TO_PHP_74
    ]);
};