/.idea
/app/*
!/app/code
!/app/design
!/app/etc
/app/etc/*
!/app/etc/config.php
/bin/*
!/bin/n98-magerun2
/db
/dev
!/dev/tools/frontools
/lib/web
/lib/.htaccess
/phpserver
/generated
/pub
/setup
/var
/vendor
/.htaccess
/.htaccess.sample
/.php_cs
/.travis.yml
/.travis.yml.sample
/CHANGELOG.md
/CONTRIBUTING.md
/CONTRIBUTOR_LICENSE_AGREEMENT.html
/COPYING.txt
/SECURITY.md
/Gruntfile.js
/index.php
/LICENSE.txt
/LICENSE_AFL.txt
/nginx.conf.sample
/package.json
/php.ini.sample
/.php_cs.dist
/.php_cs.cache
/auth.json.sample
/grunt-config.json.sample
/Gruntfile.js.sample
/ISSUE_TEMPLATE.md
/package.json.sample
/PULL_REQUEST_TEMPLATE.md
/.user.ini
/.github
/config/etc/env.php
/lib/internal/GnuFreeFont
/lib/internal/LinLibertineFont
README_EE.md
translations.csv
src.tar.gz
LICENSE_EE.txt
/tools
!/lib/limesoda
!/lib/fooman
.editorconfig
/log/capistrano.log
/.php-cs-fixer.dist.php
