volumes:
    magento-redis:

services:
  app:
    image: copex/nginx-php-fpm:dev
    ports:
      - "80:80"
      - "443:443"
    extra_hosts:
        - "host.docker.internal:host-gateway"
    environment:
      DOMAIN: ${DEV_DOMAIN}
      MAGENTO_ROOT: ${DEV_MAGENTO_ROOT}
      XDEBUG_MODE: debug
      XDEBUG_CONFIG: "client_host=host.docker.internal log_level=0 start_with_request=yes remote_enable=1 remote_mode=req remote_port=9000 remote_host=host.docker.internal remote_connect_back=0 remote_autostart=0 idekey=PHPSTORM"
      PHP_IDE_CONFIG: "serverName=${DEV_DOMAIN}"
      PAGESPEED: 0
      SSH_AUTH_SOCK: $SSH_AUTH_SOCK
    depends_on:
        -   redis
    volumes:
      - ../:${DEV_MAGENTO_ROOT}
      - $SSH_AUTH_SOCK:$SSH_AUTH_SOCK
  mysql:
    environment:
      MYSQL_ROOT_PASSWORD: "r00t"
      MYSQL_USER: "magento"
      MYSQL_PASSWORD: "magento"
      MYSQL_DATABASE: "magento"
    ports:
      - "3306:3306"
  redis:
      image: redis:latest
      restart: unless-stopped
      container_name: "${PROJECT}-redis"
      volumes:
          - magento-redis:/data
      networks:
          - backend
  elastichq:
      image: elastichq/elasticsearch-hq
      networks:
          - backend
      ports:
          - 5000:5000
      environment:
          HQ_DEFAULT_URL: 'http://search:9200'
  mailcatcher:
      image: sj26/mailcatcher
      ports:
          - "1080:1080"
      networks:
          - backend