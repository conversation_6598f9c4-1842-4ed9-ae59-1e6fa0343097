<?php
return [
    'modules' => [
        'Magento_WebapiAsync' => 0,
        'Magento_TwoFactorAuth' => 0,
        'Magento_LoginAsCustomerAssistance' => 0,
        'MSP_Common' => 1,
        'MSP_DevTools' => 1
    ],
    'backend' => [
        'frontName' => 'backoffice'
    ],
    'install' => [
        'date' => 'Tue, 15 Jun 2018 08:43:05 +0000'
    ],
    'crypt' => [
        'key' => '84e9d819d0e98652d3e795c6db22f980'
    ],
    'session' => [
        'save' => 'redis',
        'redis' => [
            'host' => 'redis',
            'port' => '6379',
            'password' => '',
            'timeout' => '2.5',
            'persistent_identifier' => '',
            'database' => '2',
            'compression_threshold' => '2048',
            'compression_library' => 'gzip',
            'log_level' => '1',
            'max_concurrency' => '20',
            'break_after_frontend' => '5',
            'break_after_adminhtml' => '30',
            'first_lifetime' => '600',
            'bot_first_lifetime' => '60',
            'bot_lifetime' => '7200',
            'disable_locking' => '1',
            'min_lifetime' => '60',
            'max_lifetime' => '2592000'
        ]
    ],
    'cache' => [
        'frontend' => [
            'default' => [
                'backend' => 'Cm_Cache_Backend_Redis',
                'backend_options' => [
                    'server' => 'redis',
                    'port' => '6379'
                ],
                'id_prefix' => '26b_'
            ],
            'page_cache' => [
                'backend' => 'Cm_Cache_Backend_Redis',
                'backend_options' => [
                    'server' => 'redis',
                    'port' => '6379',
                    'database' => '1',
                    'compress_data' => '0'
                ],
                'id_prefix' => '26b_'
            ]
        ]
    ],
    'db' => [
        'table_prefix' => '',
        'connection' => [
            'default' => [
                'host' => 'mysql',
                'dbname' => 'magento',
                'username' => 'magento',
                'password' => 'magento',
                'active' => '1'
            ]
        ]
    ],
    'resource' => [
        'default_setup' => [
            'connection' => 'default'
        ]
    ],
    'x-frame-options' => 'SAMEORIGIN',
    'MAGE_MODE' => 'developer',
    'cache_types' => [
        'config' => 1,
        'layout' => 1,
        'block_html' => 1,
        'collections' => 1,
        'reflection' => 1,
        'db_ddl' => 1,
        'eav' => 1,
        'config_integration' => 1,
        'config_integration_api' => 1,
        'full_page' => 0,
        'translate' => 1,
        'config_webservice' => 1,
        'compiled_config' => 1,
        'customer_notification' => 1
    ],
    'directories' => [
        'document_root_is_pub' => true
    ],
    'system' => [
        'stores' => [

        ],
        'default' => [
            'catalog' => [
                'search' => [
                    'engine' => 'elasticsearch7',
                    'elasticsearch7_server_hostname' => 'search'
                ]
            ],
            'uniwien_oauth2' => [
                'settings' => [
                    'is_enabled' => 1,
                    'automatic_redirect' => 0,
                    'client_id' => 'akademische-feiern.univie.ac.at',
                    'client_secret' => 'UazqALi3Sjt8CZ5b1MB2K_sPUeidx7G-LFInPFaIBeGKJEOk5XK8BycUi4CgX8PJbTVBZa6gAqnP-F6hoBFugA',
                    'oauth2_service_uri' => 'https://auth-test.univie.ac.at/',
                    'oauth2_auth_uri' => 'https://auth-test.univie.ac.at/authorize',
                    'oauth2_token_uri' => 'https://auth-test.univie.ac.at/token',
                    'oauth2_scopes' => 'studiendaten',
                    'redirect_uri_customer' => 'http://www.univie-eventshop.test/',
                    'redirect_uri_admin' => 'http://www.univie-eventshop.test/backoffice'
                ]
            ],
            'limesoda_rt_ticket_system' => [
                'settings' => [
                    'is_enabled' => 0
                ]
            ],
            'limesoda_groupcatalog' => [
                'general' => [
                    'is_enabled' => 0
                ]
            ],
            'system' => [
                'gmailsmtpapp' => [
                    'active' => 0
                ],
                'backup' => [
                    'functionality_enabled' => 0
                ]
            ],
            'admin' => [
                'security' => [
                    'session_lifetime' => 31536000,
                    'password_lifetime' => '',
                    'password_is_forced' => 0
                ]
            ],
            'smile_elasticsuite_core_base_settings' => [
                'es_client' => [
                    'servers' => 'search:9200'
                ]
            ],
            'klarna' => [
                'api' => [
                    'test_mode' => 1,
                    'debug' => 1
                ]
            ],
            'xtcore' => [
                'adminnotification' => [
                    'enabled' => 0
                ]
            ],
            'dev' => [
                'debug' => [
                    'debug_logging' => '1'
                ],
                'js' => [
                    'merge_files' => '0',
                    'minify_files' => '0'
                ],
                'css' => [
                    'minify_files' => '0',
                    'merge_css_files' => '0'
                ],
                'template' => [
                    'minify_html' => '0',
                    'allow_symlink' => '1'
                ],
                'quickdevbar' => [
                    'enable' => 1
                ],
                'image' => [
                    'default_adapter' => 'IMAGEMAGICK'
                ]
            ],
            'smile_debugtoolbar' => [
                'configuration' => [
                    'enabled' => 1
                ]
            ],
            'msp_devtools' => [
                'general' => [
                    'enabled' => 1
                ]
            ],
            'wirecard_checkoutpage' => [
                'basicdata' => [
                    'configuration' => 'demo'
                ]
            ],
            'paypal' => [
                'wpp' => [
                    'sandbox_flag' => 1
                ]
            ],
            'payment' => [
                'amazon_payment' => [
                    'sandbox' => 1
                ],
                'checkmo' => [
                    'active' => 1
                ]
            ],
            'worldline_connection' => [
                'connection' => [
                    'environment_mode' => '0',
                    'api_key' => '7A6E7B68ACBA8D08B0F2',
                    'merchant_id' => 'COPEXTEST'
                ]
            ],
            'mailchimp' => [
                'general' => [
                    'active' => 0
                ]
            ],
            'web' => [
                'secure' => [
                    'base_url' => 'http://www.univie-eventshop.test/',
                    'base_link_url' => 'http://www.univie-eventshop.test/'
                ],
                'unsecure' => [
                    'base_url' => 'http://www.univie-eventshop.test/',
                    'base_link_url' => 'http://www.univie-eventshop.test/'
                ]
            ],
            'studentmanagement' => [
                'domain' => 'https://api-test.univie.ac.at/akademische-feiern/v1/abgeschlossenestudien'
            ],
            'copex_productlimitation' => [
                'general' => [
                    'enabled' => 1,
                    'max_bought_qty' => 20,
                    'max_per_product_cart_qty' => 1
                ]
            ],
            'jira_service_desk_integration' => [
                'settings' => [
                    'enabled' => '1',
                    'request_url' => 'https://jsd-test-01.univie.ac.at/',
                    'project_key' => 'FICOP',
                    'request_type_id' => '1169',
                    'username' => 'pointnera61',
                    'api_token' => 'qXEheMlRN4DtVfXt46MCeVN2BA6abmef1uSoaP',
                    'store_name' => 'Veranstaltungsmanagement',
                    'products' => 1
                ]
            ],
            'disable_compare' => [
                'general' => [
                    'enable' => 1
                ]
            ]
        ]
    ],
    'downloadable_domains' => [
        'www.magento.local'
    ]
];
