Index: Encryption/Encryptor.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Encryption/Encryptor.php b/Encryption/Encryptor.php
--- a/Encryption/Encryptor.php
+++ b/Encryption/Encryptor.php	(date 1668591157782)
@@ -80,7 +80,7 @@

     public const CIPHER_AEAD_CHACHA20POLY1305 = 3;

-    public const CIPHER_LATEST = 3;
+    public const CIPHER_LATEST = 2;
     /**#@-*/

     /**
Index: Encryption/Encryptor.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Encryption/Encryptor.php b/Encryption/Encryptor.php
--- a/Encryption/Encryptor.php
+++ b/Encryption/Encryptor.php	(date 1668605256379)
@@ -156,7 +156,7 @@
      */
     public function getLatestHashVersion(): int
     {
-        return self::HASH_VERSION_ARGON2ID13_AGNOSTIC;
+        return self::HASH_VERSION_SHA256;
     }

     /**
Index: Encryption/Encryptor.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Encryption/Encryptor.php b/Encryption/Encryptor.php
--- a/Encryption/Encryptor.php
+++ b/Encryption/Encryptor.php	(date 1658816883443)
@@ -375,11 +375,7 @@
      */
     public function encrypt($data)
     {
-        $crypt = new SodiumChachaIetf($this->keys[$this->keyVersion]);
-
-        return $this->keyVersion .
-            ':' . self::CIPHER_AEAD_CHACHA20POLY1305 .
-            ':' . base64_encode($crypt->encrypt($data));
+        return $this->encryptWithFastestAvailableAlgorithm($data);
     }

     /**
