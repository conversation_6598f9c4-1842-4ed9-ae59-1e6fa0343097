Index: View/Element/Html/Calendar.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/View/Element/Html/Calendar.php b/View/Element/Html/Calendar.php
--- a/View/Element/Html/Calendar.php
+++ b/View/Element/Html/Calendar.php	(date 1706614974659)
@@ -111,8 +111,8 @@
         $this->assignFieldsValues($localeData);

         // get "am" & "pm" words
-        $this->assign('am', $this->encoder->encode($localeData['calendar']['gregorian']['AmPmMarkers']['0']));
-        $this->assign('pm', $this->encoder->encode($localeData['calendar']['gregorian']['AmPmMarkers']['1']));
+        $this->assign('am', $this->encoder->encode($localeData['calendar']['gregorian']['AmPmMarkersAbbr'][0] ?? null));
+        $this->assign('pm', $this->encoder->encode($localeData['calendar']['gregorian']['AmPmMarkersAbbr'][1] ?? null));

         // get first day of week and weekend days
         $this->assign(
