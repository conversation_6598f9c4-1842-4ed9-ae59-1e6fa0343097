Index: Stdlib/Cookie/PhpCookieManager.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Stdlib/Cookie/PhpCookieManager.php b/Stdlib/Cookie/PhpCookieManager.php
--- a/Stdlib/Cookie/PhpCookieManager.php
+++ b/Stdlib/Cookie/PhpCookieManager.php	(date 1746609248000)
@@ -31,7 +31,7 @@
      * RFC 2109 - Page 15
      * http://www.ietf.org/rfc/rfc6265.txt
      */
-    private const MAX_NUM_COOKIES = 50;
+    private const MAX_NUM_COOKIES = 100;
     public const MAX_COOKIE_SIZE = 4096;
     private const EXPIRE_NOW_TIME = 1;
     private const EXPIRE_AT_END_OF_SESSION_TIME = 0;
