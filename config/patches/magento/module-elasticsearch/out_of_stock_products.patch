Index: Model/ResourceModel/Fulltext/Collection/SearchResultApplier.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Model/ResourceModel/Fulltext/Collection/SearchResultApplier.php b/Model/ResourceModel/Fulltext/Collection/SearchResultApplier.php
https://stackoverflow.com/a/73991266
--- a/Model/ResourceModel/Fulltext/Collection/SearchResultApplier.php	
+++ b/Model/ResourceModel/Fulltext/Collection/SearchResultApplier.php	(date 1671629824446)
@@ -169,7 +169,7 @@
     {
         $ids = [];
 
-        if (!$this->hasShowOutOfStockStatus()) {
+        if ($this->hasShowOutOfStockStatus()) {
             return $ids;
         }
 
