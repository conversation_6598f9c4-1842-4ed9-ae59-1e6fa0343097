<?php

namespace Deployer;

/**
 * Hosts
 */
host('staging')
    ->setHostname(getenv('CI') === 'true' ? 'abschlussfeier-anmeldung.univie.ac.at' : 'univie-veranstaltung')
    ->setPort('22')
    ->setConfigFile('~/.ssh/config')
    ->setRemoteUser('pointnera61')
    ->setDeployPath('{{deployment_root_path}}/staging')
    ->setForwardAgent(true)
    ->setLabels([
        'env'  => ENV_STAGING,
    ])
    ->set('branch', 'develop');

if(get('docker')){
    host('staging')->setDeployPath('{{deployment_root_path}}/{{application}}');
}