<?php

namespace Deployer;

/**
 * Hosts
 */
host('production')
    ->setHostname(getenv('CI') === 'true' ? 'abschlussfeier-anmeldung.univie.ac.at' : 'univie-veranstaltung')
    ->setPort('22')
    ->setConfigFile('~/.ssh/config')
    ->setRemoteUser('pointnera61')
    ->setDeployPath('{{deployment_root_path}}/live')
    ->setForwardAgent(true)
    ->set('application', 'live')
    ->setLabels([
        'env'  => ENV_PRODUCTION,
    ])
    ->set('branch', 'master');

if(get('docker')){
    host('production')->setDeployPath('{{deployment_root_path}}/{{application}}');
}
