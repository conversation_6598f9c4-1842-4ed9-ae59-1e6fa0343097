<?php

namespace Deployer;

task('db:download', function () {
    $env = get('labels')['env'];
    if ($env == ENV_PRODUCTION) {
        if(get('docker')) {
            run('docker exec -u $(id -u ${USER}):www-data {{application}}-app {{release_or_current_path}}/bin/n98-magerun2 --root-dir={{release_or_current_path}} --skip-root-check db:dump --strip="@stripped" -c gzip /backups/database/database_bkp.sql.gz');
        }
        else {
            run('{{release_or_current_path}}/bin/n98-magerun2 --root-dir={{release_or_current_path}} --skip-root-check db:dump --strip="@stripped" -c gzip /var/tmp/database_bkp.sql.gz');
        }
        download("/var/tmp/database_bkp.sql.gz",
            "./db/import/database_bkp_" . date('Y.m.d-H:i:s') . ".sql.gz");
        run("rm /var/tmp/database_bkp.sql.gz");
    }

})->desc('Connects to remote host, take a dump from the database via n98-magerun and download to local db/import folder');