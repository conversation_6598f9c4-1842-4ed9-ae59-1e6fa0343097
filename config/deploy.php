<?php

namespace Deployer;

use Deployer\Task\Context;

require 'recipe/magento2.php';
//require __DIR__ . '/vendor/deployer/deployer/contrib/rsync.php';

const ENV_PRODUCTION = 'production';
const ENV_STAGING = 'staging';

set('docker', false);

//deploy targets
include __DIR__ . '/deploy/production.php';
include __DIR__ . '/deploy/staging.php';


//custom tasks
include __DIR__ . '/deploy/tasks/dbTasks.php';
include __DIR__ . '/deploy/tasks/miscTasks.php';


/**
 * Settings
 */
set('repository', '*******************:CopeX/uniwien-veranstaltungsshop.git');
set('application', 'univie-veranstaltungsshop');
set('deployment_root_path', '/opt/www');
set('keep_releases', 2);
set('static_content_jobs', '2');
set('php_version', "7.4");
set('shared_files', [
    'var/.maintenance.ip',
    'var/.setup_cronjob_status',
    'var/.update_cronjob_status',
    'pub/.htaccess'
]);
set('static_content_locales', 'de_DE en_US');
set('magento_themes', ['Magento/backend','UniWienEventshop/blank']);

if(substr(get('deployment_root_path'),-1) === "/"){
    set('deployment_root_path', substr(get('deployment_root_path'),0,-1));
}

set('http_user','apache');
set('http_group','scrum');
set('writable_dirs', [
    'var',
    'pub/static',
//    'pub/media',
    'generated',
    'var/page_cache'
]);

//composer command
set('bin/composer', function () {
    return '/opt/www/composer --ignore-platform-req=ext-sodium';
});

if(get('docker')){
    //set php executable to docker container
    set('bin/php', 'docker exec -u $(id -u ${USER}):www-data {{application}}-app php');

    //composer command
    set('bin/composer', function () {
        return 'docker run --rm  -w="{{release_or_current_path}}" -v {{deployment_root_path}}/{{application}}:{{deployment_root_path}}/{{application}} -v /var/.composer:/.composer -u $(id -u ${USER}):www-data -e COMPOSER_MEMORY_LIMIT=-1 copex/php:{{php_version}} composer';
    });

    //define n98-magerun command
    set('bin/n98-magerun2', function(){
        return 'docker run --rm  -w="{{release_or_current_path}}" -v {{deployment_root_path}}/{{application}}:{{deployment_root_path}}/{{application}} -u $(id -u ${USER}):www-data -e COMPOSER_MEMORY_LIMIT=-1 copex/php:{{php_version}} ./bin/n98-magerun2';
    });

    task('restart_docker', function(){
        run("cd {{deployment_root_path}}/{{application}} && docker-compose restart app");
    });

}


//copy env.php file
task('copy_config', function () {
    $env = get('labels')['env'];
    if($env == ENV_PRODUCTION){
        upload('config/etc/env.prod.php', '{{release_or_current_path}}/app/etc/env.php');
    } elseif ($env == ENV_STAGING) {
        upload('config/etc/env.staging.php', '{{release_or_current_path}}/app/etc/env.php');
    }
});

task('build_hyva', function(){
    if(get('hyva_theme')){
        if(get('docker')){
            run('docker run -u $(id -u ${USER}):www-data -w="{{release_or_current_path}}/app/design/frontend/{{hyva_theme}}/web/tailwind" -v {{deployment_root_path}}/{{application}}:{{deployment_root_path}}/{{application}} node:14-alpine npm ci');
            run('docker run -u $(id -u ${USER}):www-data -w="{{release_or_current_path}}/app/design/frontend/{{hyva_theme}}/web/tailwind" -v {{deployment_root_path}}/{{application}}:{{deployment_root_path}}/{{application}} node:14-alpine npm run build-prod');
        }else {
            run('{{release_or_current_path}}/app/design/frontend/{{hyva_theme}}/web/tailwind npm ci');
            run('{{release_or_current_path}}/app/design/frontend/{{hyva_theme}}/web/tailwind npm run build-prod');
        }
    }
});

desc("disable fpc");
task("disable_fpc", function (){
    run("{{release_or_current_path}}/bin/magento cache:disable full_page");
});


desc("set permissions to cache folder");
task('cache_permissions', function(){
    run('chmod g+w -Rf {{release_or_current_path}}/var');
});


desc("Deploy Snowdog theme");
task('deploy_theme', function(){
    run('npm install --prefix {{release_or_current_path}}/vendor/snowdog/frontools --no-progress --no-spin');
    run('npm install gulp');
    run('{{release_or_current_path}}/vendor/snowdog/frontools/node_modules/.bin/gulp styles --gulpfile {{release_or_current_path}}/vendor/snowdog/frontools/gulpfile.esm.js --area frontend --lang scss --prod');
});

desc("Reload Server");
task('reload_server', function(){
    run('sudo systemctl restart php-fpm');
});

desc("Remove repo because of permission problems");
task('remove_repo', function(){
    run('rm -r {{deploy_path}}/.dep/repo');
});

//after('deploy:update_code', 'change_owner');
after('deploy:failed', 'deploy:unlock');
before('magento:compile', 'copy_config');
before('magento:deploy:assets', 'build_hyva');
after('magento:deploy:assets', 'deploy_theme');
after("deploy:symlink", "cache_permissions");
after("deploy:symlink", "reload_server");
before("reload_server", "disable_fpc");
before('deploy:update_code','remove_repo');

if (get('docker')) {
    after('deploy:symlink', 'restart_docker');
}

//override deploy to make it with rsync
//task('deploy', [
//    'deploy:prepare', // prepare fails as it depends on update_code as it depends on repository
//    'rsync',
//    'deploy:clear_paths',
//    'deploy:magento',
//    'deploy:publish',
//])->desc('Deploy your project');
