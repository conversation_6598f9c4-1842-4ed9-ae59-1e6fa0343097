# Project Documentation in Slite

Requirements:
* PHP version 7.2
* current version of m98-magerun2 (in bin folder) - delete it, if you are not sure, to automatically get current version during installation
* Node Version >=12.13.0 <13.0.0

## Installation and theme compilation
### Install Project
```
magento start
magento install
```

##### Install n via npm and change node version to 12.13.0 (inside app)
```
magento enter app
npm install -g n
n 12.13.0
```

##### Install snowdog frontools node modules (inside app)
```
npm install --prefix vendor/snowdog/frontools --no-progress --no-spin
```

##### Setup theme config files if no configuration exist. This command creates a convenient symlink from /tools to /vendor/snowdog/frontools and copies all sample files. (inside app)
```
npm run setup --prefix vendor/snowdog/frontools
```

##### Process theme styles. This command manually triggers styles processing pipeline. (inside app)
```
npm run styles --prefix vendor/snowdog/frontools
```

It should also be possible to process styles inside tools folder (outside app).
```
cd tools
npm rebuild node-sass   # only first time
npm run styles
```

It could be necessary to run setup:upgrade and/or app:config:import and to fix permissions once again (outside app)
```
magento m setup:upgrade
magento m app:config:import
magento fixpermissions
```

### If errors occur, fix permissions (outside app) and rebuild (inside app)
```
magento fixpermissions
```

```
npm rebuild --prefix vendor/snowdog/frontools --no-progress --no-spin
```
