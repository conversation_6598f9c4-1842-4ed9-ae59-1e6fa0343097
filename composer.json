{"name": "magento/project-community-edition", "description": "eCommerce Platform for Growth (Community Edition)", "type": "project", "version": "2.4.4", "license": ["proprietary"], "repositories": {"magento": {"type": "composer", "url": "https://repo.magento.com/"}, "copex-repo": {"type": "composer", "url": "https://repo.copex.io/univie-349e597bd50a3eef5506c25f33ccb367/"}}, "require": {"magento/product-community-edition": "2.4.5-p13", "cweagans/composer-patches": "^1.6", "uniwien/theme-blank-sass": "~1.0", "jumbojett/openid-connect-php": "^v0.9.0", "pear/http_request2": "~v2.4.0", "fooman/pdfcustomiser-m2": "^8.3", "avstudnitz/scopehint2": "1.3.0", "copex/module-customernumber": "^1", "splendidinternet/mage2-locale-de-de": "^1.16", "uniwien/oauth2": "~1.1", "ethanyehuda/magento2-cronjobmanager": "^1.11", "mageworx/module-optiontemplatesmeta": "~2", "thampe/module-customer-password": "~1", "bsscommerce/disable-compare": "~1", "experius/module-emailcatcher": "^3.1.3", "copex/uniwien-sap-export": "^1.0", "customgento/module-admin-payment-m2": "^1.1", "magento/composer-root-update-plugin": "~2.0", "magento/composer-dependency-version-audit-plugin": "~0.1", "amasty/module-order-status": "^1.1", "sashas/module-product-alert-grid": "^1.0", "actiview/module-honeypot": "^0.1.3", "worldline/module-magento-payment": "^2.13", "yireo/magento2-disable-csp": "^1.0"}, "require-dev": {"allure-framework/allure-phpunit": "~1.2.0", "lusitanian/oauth": "~0.8.10", "symfony/finder": "~5.4.0", "mage2tv/magento-cache-clean": "*", "magento/magento-coding-standard": "~3.0.0", "pdepend/pdepend": "2.5.2", "yireo/magento2-whoops": "*", "msp/devtools": "*", "rector/rector": "^0.14.8"}, "config": {"use-include-path": true, "allow-plugins": {"magento/magento-composer-installer": true, "magento/inventory-composer-installer": true, "cweagans/composer-patches": true, "laminas/laminas-dependency-plugin": true, "magento/composer-dependency-version-audit-plugin": false, "magento/composer-root-update-plugin": true, "dealerdirect/phpcodesniffer-composer-installer": true}}, "autoload": {"psr-4": {"Magento\\Framework\\": "lib/internal/Magento/Framework/", "Magento\\Setup\\": "setup/src/Magento/Setup/", "Magento\\": "app/code/Magento/"}, "psr-0": {"": ["app/code/"]}, "files": ["app/etc/NonComposerComponentRegistration.php"], "exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**"]}, "autoload-dev": {"psr-4": {"Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/", "Magento\\ToolkitFramework\\": "dev/tools/performance-toolkit/framework/Magento/ToolkitFramework/"}}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"magento-force": "override", "composer-exit-on-patch-failure": true, "magento-deploy-ignore": {"*": ["/pub/.htaccess"]}, "patches": {"magento/framework": {"nosodium": "config/patches/magento/framework/nosodium.patch", "calendar": "config/patches/magento/framework/38214.patch", "VULN-32437": "https://gist.githubusercontent.com/kristof-ringleff/76e36b37a4855148973928939f0e6248/raw/82e6436a7b0fbb1dff2168776d2ae9ce90e2f228/VULN-32437-2-4-X.diff"}, "magento/magento2-base": {"mage-menu-mobile-fix": "config/patches/magento/magento2-base/mobile-menu-android.patch"}}}, "replace": {"amzn/amazon-pay-and-login-magento-2-module": "*", "dotmailer/dotmailer-magento2-extension": "*", "dotmailer/dotmailer-magento2-extension-package": "*", "dotmailer/dotmailer-magento2-extension-enterprise": "*", "dotmailer/dotmailer-magento2-extension-chat": "*", "magento/module-marketplace": "*", "magento/module-authorizenet": "*", "magento/module-sample-data": "*", "magento/module-send-friend": "*", "magento/module-swagger": "*", "magento/module-swagger-webapi": "*", "magento/module-swagger-webapi-async": "*", "magento/module-signifyd": "*", "shopialfb/facebook-module": "*", "temando/module-shipping": "*", "temando/module-shipping-m2": "*", "vertex/product-magento-module": "*", "vertex/module-address-validation": "*", "vertex/module-tax": "*", "vertex/sdk": "*", "vertexinc/product-magento-module": "*", "yotpo/magento2-module-yotpo-reviews": "*", "yotpo/magento2-module-yotpo-reviews-bundle": "*", "magento/module-advanced-pricing-import-export": "*", "magento/module-analytics": "*", "magento/module-bundle-import-export": "*", "magento/module-catalog-analytics": "*", "magento/module-customer-analytics": "*", "magento/module-customer-import-export": "*", "magento/module-cybersource": "*", "magento/module-dhl": "*", "magento/module-downloadable-import-export": "*", "magento/module-eway": "*", "magento/module-fedex": "*", "magento/module-google-adwords": "*", "magento/module-grouped-import-export": "*", "magento/module-multishipping": "*", "magento/module-persistent": "*", "magento/module-quote-analytics": "*", "magento/module-review-analytics": "*", "magento/module-sales-analytics": "*", "magento/module-tax-import-export": "*", "magento/module-ups": "*", "magento/module-usps": "*", "magento/module-version": "*", "magento/module-wishlist-analytics": "*", "magento/module-worldpay": "*", "paypal/module-braintree": "*", "braintree/braintree_php": "*", "braintree/braintree": "*", "paypal/module-braintree-core": "*", "paypal/module-braintree-graph-ql": "*", "magento/module-bundle-graph-ql": "*", "magento/module-catalog-graph-ql": "*", "magento/module-catalog-url-rewrite-graph-ql": "*", "magento/module-cms-graph-ql": "*", "magento/module-cms-url-rewrite-graph-ql": "*", "magento/module-configurable-product-graph-ql": "*", "magento/module-directory-graph-ql": "*", "magento/module-downloadable-graph-ql": "*", "magento/module-eav-graph-ql": "*", "magento/module-authorizenet-graph-ql": "*", "magento/module-checkout-agreements-graph-ql": "*", "magento/module-related-product-graph-ql": "*", "magento/module-grouped-product-graph-ql": "*", "magento/module-sales-graph-ql": "*", "magento/module-send-friend-graph-ql": "*", "magento/module-store-graph-ql": "*", "magento/module-swatches-graph-ql": "*", "magento/module-tax-graph-ql": "*", "magento/module-theme-graph-ql": "*", "magento/module-paypal-graph-ql": "*", "magento/module-url-rewrite-graph-ql": "*", "magento/module-vault-graph-ql": "*", "magento/module-weee-graph-ql": "*", "magento/module-wishlist-graph-ql": "*", "magento/module-admin-analytics": "*", "magento/module-adobe-ims": "*", "magento/module-admin-adobe-ims": "*", "magento/module-adobe-ims-api": "*", "magento/module-adobe-stock-admin-ui": "*", "magento/module-adobe-stock-asset": "*", "magento/module-adobe-stock-asset-api": "*", "magento/module-adobe-stock-client": "*", "magento/module-adobe-stock-client-api": "*", "magento/module-adobe-stock-image": "*", "magento/module-adobe-stock-image-admin-ui": "*", "magento/module-adobe-stock-image-api": "*", "magento/module-catalog-cms-graph-ql": "*", "magento/module-customer-downloadable-graph-ql": "*", "magento/module-catalog-customer-graph-ql": "*", "klarna/module-core": "*", "klarna/module-kp": "*", "klarna/module-kp-graph-ql": "*", "klarna/module-onsitemessaging": "*", "klarna/module-ordermanagement": "*", "magento/module-inventory-quote-graph-ql": "*", "magento/module-catalog-inventory-graph-ql": "*", "magento/module-catalog-rule-graph-ql": "*", "magento/module-compare-list-graph-ql": "*", "magento/module-gift-message-graph-ql": "*", "magento/module-inventory-in-store-pickup-graph-ql": "*", "magento/module-inventory-in-store-pickup-quote-graph-ql": "*", "magento/module-login-as-customer-graph-ql": "*", "magento/module-re-captcha-webapi-graph-ql": "*", "magento/module-review-graph-ql": "*", "magento/module-catalog-page-builder-analytics": "*", "magento/module-cms-page-builder-analytics": "*", "magento/module-page-builder-analytics": "*", "magento/module-page-builder-admin-analytics": "*", "magento/module-braintree-graph-ql": "*", "magento/module-catalog-customer-ql": "*", "magento/module-catalog-permissions-graph-ql": "*", "magento/module-catalog-staging-graph-ql": "*", "magento/module-customer-balance-graph-ql": "*", "magento/module-gift-card-account-graph-ql": "*", "magento/module-gift-card-graph-ql": "*", "magento/module-gift-wrapping-graph-ql": "*", "magento/module-inventory-graph-ql": "*", "magento/module-multiple-wishlist-graph-ql": "*", "magento/module-newsletter-graph-ql": "*", "magento/module-reward-graph-ql": "*", "magento/module-rma-graph-ql": "*", "magento/module-staging-graph-ql": "*", "magento/module-target-rule-graph-ql": "*", "magento/module-versions-cms-url-rewrite-graph-ql": "*", "magento/module-payment-graph-ql": "*", "magento/module-two-factor-auth": "*", "magento/module-inventory-import-export": "*", "magento/module-google-gtag": "*", "magento/module-google-optimizer": "*"}}